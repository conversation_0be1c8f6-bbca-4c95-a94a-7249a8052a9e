/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.jsx&appDir=C%3A%5CUsers%5Cdavid%5CDesktop%5CProjekty%5Cblog_template%5Cmy-travel-blog%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdavid%5CDesktop%5CProjekty%5Cblog_template%5Cmy-travel-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.jsx&appDir=C%3A%5CUsers%5Cdavid%5CDesktop%5CProjekty%5Cblog_template%5Cmy-travel-blog%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdavid%5CDesktop%5CProjekty%5Cblog_template%5Cmy-travel-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.jsx */ \"(rsc)/./src/app/layout.jsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.jsx */ \"(rsc)/./src/app/error.jsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.jsx */ \"(rsc)/./src/app/not-found.jsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.jsx */ \"(rsc)/./src/app/page.jsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\"],\n'error': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\error.jsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\not-found.jsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.jsx&appDir=C%3A%5CUsers%5Cdavid%5CDesktop%5CProjekty%5Cblog_template%5Cmy-travel-blog%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdavid%5CDesktop%5CProjekty%5Cblog_template%5Cmy-travel-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5C%40vercel%5C%5Canalytics%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5C%40vercel%5C%5Cspeed-insights%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.jsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Montserrat%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CAsyncCSS.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CClientAnalytics.jsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CClientResourcePreloader.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CCookieConsent.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CSmoothScroll.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CWebVitalsMonitor.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5C%40vercel%5C%5Canalytics%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5C%40vercel%5C%5Cspeed-insights%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.jsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Montserrat%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CAsyncCSS.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CClientAnalytics.jsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CClientResourcePreloader.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CCookieConsent.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CSmoothScroll.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CWebVitalsMonitor.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@vercel/analytics/dist/react/index.mjs */ \"(rsc)/./node_modules/@vercel/analytics/dist/react/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@vercel/speed-insights/dist/react/index.mjs */ \"(rsc)/./node_modules/@vercel/speed-insights/dist/react/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AsyncCSS.jsx */ \"(rsc)/./src/components/AsyncCSS.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientAnalytics.jsx */ \"(rsc)/./src/components/ClientAnalytics.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientResourcePreloader.jsx */ \"(rsc)/./src/components/ClientResourcePreloader.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/CookieConsent.jsx */ \"(rsc)/./src/components/CookieConsent.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PWAInstaller.jsx */ \"(rsc)/./src/components/PWAInstaller.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SmoothScroll.jsx */ \"(rsc)/./src/components/SmoothScroll.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/WebVitalsMonitor.jsx */ \"(rsc)/./src/components/WebVitalsMonitor.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5C%40vercel%5C%5Canalytics%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5C%40vercel%5C%5Cspeed-insights%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.jsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Montserrat%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CAsyncCSS.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CClientAnalytics.jsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CClientResourcePreloader.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CCookieConsent.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CSmoothScroll.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CWebVitalsMonitor.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(rsc)/./node_modules/next/dist/client/image-component.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2RhdmlkJTVDJTVDRGVza3RvcCU1QyU1Q1Byb2pla3R5JTVDJTVDYmxvZ190ZW1wbGF0ZSU1QyU1Q215LXRyYXZlbC1ibG9nJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNhcHAtZGlyJTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMl9fZXNNb2R1bGUlMjIlMkMlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2RhdmlkJTVDJTVDRGVza3RvcCU1QyU1Q1Byb2pla3R5JTVDJTVDYmxvZ190ZW1wbGF0ZSU1QyU1Q215LXRyYXZlbC1ibG9nJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNpbWFnZS1jb21wb25lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdOQUFxTTtBQUNyTTtBQUNBLHNOQUE2SiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiX19lc01vZHVsZVwiLFwiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGRhdmlkXFxcXERlc2t0b3BcXFxcUHJvamVrdHlcXFxcYmxvZ190ZW1wbGF0ZVxcXFxteS10cmF2ZWwtYmxvZ1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxhcHAtZGlyXFxcXGxpbmsuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGRhdmlkXFxcXERlc2t0b3BcXFxcUHJvamVrdHlcXFxcYmxvZ190ZW1wbGF0ZVxcXFxteS10cmF2ZWwtYmxvZ1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxpbWFnZS1jb21wb25lbnQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2RhdmlkJTVDJTVDRGVza3RvcCU1QyU1Q1Byb2pla3R5JTVDJTVDYmxvZ190ZW1wbGF0ZSU1QyU1Q215LXRyYXZlbC1ibG9nJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNhcHAtZGlyJTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMl9fZXNNb2R1bGUlMjIlMkMlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTkFBcU0iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIl9fZXNNb2R1bGVcIixcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxkYXZpZFxcXFxEZXNrdG9wXFxcXFByb2pla3R5XFxcXGJsb2dfdGVtcGxhdGVcXFxcbXktdHJhdmVsLWJsb2dcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcYXBwLWRpclxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Capp%5C%5Cerror.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Capp%5C%5Cerror.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.jsx */ \"(rsc)/./src/app/error.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2RhdmlkJTVDJTVDRGVza3RvcCU1QyU1Q1Byb2pla3R5JTVDJTVDYmxvZ190ZW1wbGF0ZSU1QyU1Q215LXRyYXZlbC1ibG9nJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZXJyb3IuanN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrSkFBNEgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGRhdmlkXFxcXERlc2t0b3BcXFxcUHJvamVrdHlcXFxcYmxvZ190ZW1wbGF0ZVxcXFxteS10cmF2ZWwtYmxvZ1xcXFxzcmNcXFxcYXBwXFxcXGVycm9yLmpzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Capp%5C%5Cerror.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/error.jsx":
/*!***************************!*\
  !*** ./src/app/error.jsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\error.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_template\\my-travel-blog\\src\\app\\error.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"073d4ba5e268\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRhdmlkXFxEZXNrdG9wXFxQcm9qZWt0eVxcYmxvZ190ZW1wbGF0ZVxcbXktdHJhdmVsLWJsb2dcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjA3M2Q0YmE1ZTI2OFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.jsx":
/*!****************************!*\
  !*** ./src/app/layout.jsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_jsx_import_Montserrat_arguments_subsets_latin_variable_font_inter_weight_200_300_400_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.jsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"weight\":[\"200\",\"300\",\"400\"],\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.jsx\\\",\\\"import\\\":\\\"Montserrat\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"weight\\\":[\\\"200\\\",\\\"300\\\",\\\"400\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_jsx_import_Montserrat_arguments_subsets_latin_variable_font_inter_weight_200_300_400_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_jsx_import_Montserrat_arguments_subsets_latin_variable_font_inter_weight_200_300_400_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var _components_Navbar_ServerNavbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Navbar/ServerNavbar */ \"(rsc)/./src/components/Navbar/ServerNavbar.jsx\");\n/* harmony import */ var _components_Footer_ServerFooter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Footer/ServerFooter */ \"(rsc)/./src/components/Footer/ServerFooter.jsx\");\n/* harmony import */ var _metadata__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./metadata */ \"(rsc)/./src/app/metadata.js\");\n/* harmony import */ var _lib_structuredData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/structuredData */ \"(rsc)/./src/lib/structuredData.js\");\n/* harmony import */ var _components_CriticalCSS__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/CriticalCSS */ \"(rsc)/./src/components/CriticalCSS.jsx\");\n/* harmony import */ var _components_ClientResourcePreloader__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/ClientResourcePreloader */ \"(rsc)/./src/components/ClientResourcePreloader.jsx\");\n/* harmony import */ var _components_AsyncCSS__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/AsyncCSS */ \"(rsc)/./src/components/AsyncCSS.jsx\");\n/* harmony import */ var _components_ClientAnalytics__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/ClientAnalytics */ \"(rsc)/./src/components/ClientAnalytics.jsx\");\n/* harmony import */ var _vercel_analytics_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @vercel/analytics/react */ \"(rsc)/./node_modules/@vercel/analytics/dist/react/index.mjs\");\n/* harmony import */ var _vercel_speed_insights_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @vercel/speed-insights/react */ \"(rsc)/./node_modules/@vercel/speed-insights/dist/react/index.mjs\");\n/* harmony import */ var _components_SmoothScroll__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../components/SmoothScroll */ \"(rsc)/./src/components/SmoothScroll.jsx\");\n/* harmony import */ var _components_CookieConsent__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../components/CookieConsent */ \"(rsc)/./src/components/CookieConsent.jsx\");\n/* harmony import */ var _components_PWAInstaller__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../components/PWAInstaller */ \"(rsc)/./src/components/PWAInstaller.jsx\");\n/* harmony import */ var _components_WebVitalsMonitor__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../components/WebVitalsMonitor */ \"(rsc)/./src/components/WebVitalsMonitor.jsx\");\n// src/app/layout.jsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Usunieto Cormorant - wszystko sans-serif dla ultra minimalizmu\n// Domyślne wartości dla metadata\nconst siteUrl = \"https://bakasana-travel.blog\" || 0;\nconst siteName = \"bakasana-travel.blog\" || 0;\nconst siteDescription = \"Odkryj piękno Bali z nami\" || 0;\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1,\n    viewportFit: 'cover',\n    themeColor: '#5a5046'\n};\nconst metadata = (0,_metadata__WEBPACK_IMPORTED_MODULE_5__.generateMetadata)({\n    title: 'Bakasana - Retreaty Jogowe Bali & Sri Lanka | Julia Jakubowicz',\n    description: '⭐ Ekskluzywne retreaty jogowe na Bali i Sri Lance z doświadczoną fizjoterapeutką. ✓ Małe grupy ✓ Transformacyjne podróże ✓ Od 2500 PLN. Odkryj harmonię ciała i ducha →',\n    keywords: [\n        'joga Bali retreat 2025',\n        'retreat jogi Sri Lanka',\n        'wyjazd joga Bali Sri Lanka',\n        'warsztaty jogi Ubud Sigiriya',\n        'retreat jogi dla początkujących',\n        'najlepszy retreat jogi opinie',\n        'ile kosztuje wyjazd na jogę',\n        'joga i medytacja polska instruktorka',\n        'bezpieczny wyjazd joga dla kobiet',\n        'ayurveda Sri Lanka joga',\n        'Julia Jakubowicz fizjoterapeutka',\n        'transformacyjne podróże jogowe'\n    ]\n});\nfunction RootLayout({ children }) {\n    const structuredData = (0,_metadata__WEBPACK_IMPORTED_MODULE_5__.generateStructuredData)({\n        type: 'TravelAgency'\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"pl\",\n        className: `${(next_font_google_target_css_path_src_app_layout_jsx_import_Montserrat_arguments_subsets_latin_variable_font_inter_weight_200_300_400_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_17___default().variable)}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CriticalCSS__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"application-name\",\n                        content: \"Bali Yoga Journey\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"Bali Yoga Journey\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#C9A961\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-tap-highlight\",\n                        content: \"no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#C9A961\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify(structuredData)\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"min-h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar_ServerNavbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                role: \"main\",\n                                className: \"relative flex-grow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"min-h-screen\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer_ServerFooter__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AsyncCSS__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientResourcePreloader__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SmoothScroll__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                     false ? /*#__PURE__*/ 0 : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'none'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CookieConsent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PWAInstaller__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WebVitalsMonitor__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\layout.jsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.jsx\n");

/***/ }),

/***/ "(rsc)/./src/app/metadata.js":
/*!*****************************!*\
  !*** ./src/app/metadata.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateBlogMetadata: () => (/* binding */ generateBlogMetadata),\n/* harmony export */   generateBlogStructuredData: () => (/* binding */ generateBlogStructuredData),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   generateStructuredData: () => (/* binding */ generateStructuredData)\n/* harmony export */ });\n/**\r\n * Funkcje pomocnicze do generowania metadanych dla App Router w Next.js\r\n * Zoptymalizowane dla retreatów jogowych na Bali\r\n */ /**\r\n * Generuje podstawowe metadane dla stron\r\n * @param {Object} options - Opcje metadanych\r\n * @returns {Object} - Obiekt metadanych zgodny z Next.js\r\n */ function generateMetadata({ title, description, keywords = [], image, url, type = 'website' } = {}) {\n    const siteName = 'Joga Bali 2025 | Retreat z Julią Jakubowicz | Od 2900 PLN';\n    const siteDescription = '⭐ Sprawdzone retreaty jogi na Bali. ✓ Polska instruktorka ✓ Małe grupy ✓ All inclusive ✓ 97% zadowolonych. Zobacz opinie →';\n    const siteImage = image || '/og-image.jpg';\n    const siteUrl = url || \"https://bakasana-travel.blog\" || 0;\n    const defaultKeywords = [\n        // Główne słowa kluczowe 2025\n        'joga Bali retreat 2025',\n        'wyjazd joga Bali',\n        'warsztaty jogi Ubud',\n        'retreat jogi dla początkujących Bali',\n        // Długi ogon - wysokiej konwersji\n        'najlepszy retreat jogi na Bali opinie',\n        'ile kosztuje wyjazd na jogę na Bali',\n        'joga i medytacja Bali polska instruktorka',\n        'bezpieczny wyjazd joga Bali dla kobiet',\n        'retreat jogi Bali all inclusive',\n        'Julia Jakubowicz instruktorka jogi',\n        'fizjoterapeutka joga Bali',\n        'małe grupy retreat jogi',\n        'transformacyjny wyjazd Bali',\n        'joga terapeutyczna Ubud',\n        'Nusa Penida joga',\n        'Gili Air retreat',\n        'tarasy ryżowe medytacja',\n        'świątynie Bali joga',\n        'RYT 500 Yoga Alliance',\n        'wellness Bali 2025'\n    ];\n    const siteKeywords = keywords.length > 0 ? [\n        ...keywords,\n        ...defaultKeywords\n    ] : defaultKeywords;\n    const fullTitle = title ? `${title} | ${siteName}` : siteName;\n    return {\n        title: fullTitle,\n        description: description || siteDescription,\n        keywords: siteKeywords,\n        metadataBase: new URL('https://bakasana-travel.blog'),\n        alternates: {\n            canonical: url || '/'\n        },\n        openGraph: {\n            title: fullTitle,\n            description: description || siteDescription,\n            url: 'https://bakasana-travel.blog' + (url || ''),\n            siteName,\n            images: [\n                {\n                    url: siteImage,\n                    width: 1200,\n                    height: 630,\n                    alt: title || 'Joga na Bali - Retreaty Jogowe'\n                }\n            ],\n            locale: 'pl_PL',\n            type\n        },\n        twitter: {\n            card: 'summary_large_image',\n            title: fullTitle,\n            description: description || siteDescription,\n            images: [\n                siteImage\n            ],\n            creator: '@julia_jakubowicz'\n        },\n        robots: {\n            index: true,\n            follow: true,\n            googleBot: {\n                index: true,\n                follow: true,\n                'max-image-preview': 'large',\n                'max-video-preview': -1,\n                'max-snippet': -1\n            }\n        },\n        verification: {\n            google: \"your-google-verification-code\"\n        },\n        category: 'Health & Wellness',\n        classification: 'Yoga Retreats',\n        other: {\n            'geo.region': 'ID-BA',\n            'geo.placename': 'Bali, Indonesia',\n            'geo.position': '-8.3405;115.0920',\n            'ICBM': '-8.3405, 115.0920'\n        }\n    };\n}\n/**\r\n * Generuje metadane dla artykułów bloga\r\n * @param {Object} post - Dane artykułu\r\n * @returns {Object} - Obiekt metadanych zgodny z Next.js\r\n */ function generateBlogMetadata(post) {\n    if (!post) return generateMetadata();\n    const blogKeywords = [\n        'joga',\n        'blog jogowy',\n        'praktyka jogi',\n        'mindfulness',\n        'wellness',\n        'zdrowie',\n        'duchowość',\n        ...post.tags || []\n    ];\n    return generateMetadata({\n        title: post.title,\n        description: post.excerpt || `${post.title} - Odkryj więcej na blogu o jodze i wellness.`,\n        keywords: blogKeywords,\n        image: post.imageUrl,\n        url: `/blog/${post.slug}`,\n        type: 'article'\n    });\n}\n/**\r\n * Generuje strukturalne dane JSON-LD dla artykułów bloga\r\n * @param {Object} post - Dane artykułu\r\n * @returns {Object} - Obiekt JSON-LD dla BlogPosting\r\n */ function generateBlogStructuredData(post) {\n    if (!post) return null;\n    return {\n        '@context': 'https://schema.org',\n        '@type': 'BlogPosting',\n        headline: post.title,\n        description: post.excerpt || post.description,\n        image: {\n            '@type': 'ImageObject',\n            url: `https://bakasana-travel.blog${post.imageUrl}`,\n            width: 1200,\n            height: 630,\n            alt: post.imageAlt || post.title\n        },\n        author: {\n            '@type': 'Person',\n            name: post.author || 'Julia Jakubowicz',\n            jobTitle: 'Instruktorka Jogi i Fizjoterapeutka',\n            url: 'https://bakasana-travel.blog/o-mnie'\n        },\n        publisher: {\n            '@type': 'Organization',\n            name: 'Joga na Bali - Julia Jakubowicz',\n            logo: {\n                '@type': 'ImageObject',\n                url: 'https://bakasana-travel.blog/og-image.jpg'\n            }\n        },\n        datePublished: post.date,\n        dateModified: post.dateModified || post.date,\n        mainEntityOfPage: {\n            '@type': 'WebPage',\n            '@id': `https://bakasana-travel.blog/blog/${post.slug}`\n        },\n        keywords: post.tags ? post.tags.join(', ') : '',\n        articleSection: post.category,\n        wordCount: post.content ? post.content.replace(/<[^>]*>/g, '').split(' ').length : 500,\n        timeRequired: `PT${Math.ceil((post.content ? post.content.replace(/<[^>]*>/g, '').split(' ').length : 500) / 200)}M`,\n        inLanguage: 'pl-PL',\n        isAccessibleForFree: true,\n        about: [\n            {\n                '@type': 'Thing',\n                name: 'Joga',\n                sameAs: 'https://pl.wikipedia.org/wiki/Joga'\n            },\n            {\n                '@type': 'Place',\n                name: 'Bali',\n                sameAs: 'https://pl.wikipedia.org/wiki/Bali'\n            }\n        ]\n    };\n}\n/**\r\n * Generuje strukturalne dane JSON-LD dla SEO\r\n * @param {Object} options - Opcje danych strukturalnych\r\n * @returns {Object} - Obiekt JSON-LD\r\n */ function generateStructuredData({ type = 'Organization', name = 'Joga na Bali - Julia Jakubowicz', description = 'Profesjonalne retreaty jogowe na Bali z certyfikowaną instruktorką i fizjoterapeutką', url = 'https://bakasana-travel.blog', logo = '/og-image.jpg', image = '/og-image.jpg', telephone = '+48 606 101 523', email = '<EMAIL>', address = {\n    addressCountry: 'PL',\n    addressLocality: 'Rzeszów'\n}, sameAs = [\n    'https://www.instagram.com/fly_with_bakasana',\n    'https://www.facebook.com/p/Fly-with-bakasana-100077568306563/',\n    'https://flywithbakasana.pl/'\n] } = {}) {\n    const baseStructure = {\n        '@context': 'https://schema.org',\n        '@type': type,\n        name,\n        description,\n        url,\n        logo: {\n            '@type': 'ImageObject',\n            url: logo\n        },\n        image,\n        telephone,\n        email,\n        address: {\n            '@type': 'PostalAddress',\n            ...address\n        },\n        sameAs\n    };\n    if (type === 'Person') {\n        return {\n            ...baseStructure,\n            '@type': 'Person',\n            jobTitle: 'Instruktorka Jogi i Fizjoterapeutka',\n            knowsAbout: [\n                'Joga',\n                'Fizjoterapia',\n                'Joga Terapeutyczna',\n                'Retreaty Jogowe',\n                'Mindfulness',\n                'Wellness'\n            ],\n            hasCredential: [\n                'Magister Fizjoterapii',\n                'RYT 500 Yoga Alliance',\n                'Certyfikowana Instruktorka Jogi'\n            ]\n        };\n    }\n    if (type === 'TravelAgency') {\n        return {\n            ...baseStructure,\n            '@type': 'TravelAgency',\n            serviceType: 'Yoga Retreats',\n            areaServed: {\n                '@type': 'Country',\n                name: 'Indonesia',\n                sameAs: 'https://en.wikipedia.org/wiki/Indonesia'\n            },\n            aggregateRating: {\n                '@type': 'AggregateRating',\n                ratingValue: '4.9',\n                reviewCount: '47',\n                bestRating: '5',\n                worstRating: '1'\n            },\n            priceRange: '2900-4500 PLN',\n            hasOfferCatalog: {\n                '@type': 'OfferCatalog',\n                name: 'Retreaty Jogowe na Bali',\n                itemListElement: [\n                    {\n                        '@type': 'Offer',\n                        itemOffered: {\n                            '@type': 'Trip',\n                            name: '12-dniowy Retreat Jogowy na Bali',\n                            description: 'Transformacyjna podróż łącząca jogę z odkrywaniem Bali',\n                            provider: {\n                                '@type': 'Person',\n                                name: 'Julia Jakubowicz'\n                            }\n                        },\n                        price: '2900',\n                        priceCurrency: 'PLN',\n                        availability: 'https://schema.org/InStock',\n                        validFrom: '2025-01-01',\n                        validThrough: '2025-12-31'\n                    }\n                ]\n            }\n        };\n    }\n    return baseStructure;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/metadata.js\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.jsx":
/*!*******************************!*\
  !*** ./src/app/not-found.jsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst metadata = {\n    title: 'Strona nie znaleziona | Fly with bakasana',\n    description: 'Przepraszamy, ale strona której szukasz nie istnieje.'\n};\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"py-32 min-h-screen bg-secondary flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto px-4 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"subtitle mb-4\",\n                    children: \"Błąd 404\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\not-found.jsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl lg:text-5xl font-serif font-light mb-6\",\n                    children: \"Strona nie została znaleziona\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\not-found.jsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg leading-relaxed mb-8 font-light opacity-70\",\n                    children: \"Przepraszamy, ale strona kt\\xf3rej szukasz nie istnieje lub została przeniesiona.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\not-found.jsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"btn-primary inline-block\",\n                            children: \"Wr\\xf3ć do strony gł\\xf3wnej\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\not-found.jsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/blog\",\n                                    className: \"text-accent hover:opacity-70 transition-opacity\",\n                                    children: \"Przejdź do bloga\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\not-found.jsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                ' · ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/program\",\n                                    className: \"text-accent hover:opacity-70 transition-opacity\",\n                                    children: \"Zobacz program\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\not-found.jsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\not-found.jsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\not-found.jsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\not-found.jsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\not-found.jsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.jsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.jsx":
/*!**************************!*\
  !*** ./src/app/page.jsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n// Usunieto niepotrzebne dane i komponenty dla ultra luksusowego minimalizmu\nconst HeroSection = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"h-screen flex items-center justify-center bg-silk relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20\",\n                style: {\n                    backgroundImage: 'url(/images/background/bali-hero.webp)'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center max-w-luxury mx-auto px-[16.67%]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-micro uppercase tracking-[0.05em] text-ink/60 mb-16 opacity-0 animate-fade-in\",\n                        style: {\n                            animationDelay: '0.2s',\n                            animationFillMode: 'forwards'\n                        },\n                        children: \"Only 8 spaces available • Next retreat: September 2025\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-hero font-extralight text-ink mb-8 opacity-0 animate-fade-in\",\n                        style: {\n                            animationDelay: '0.8s',\n                            animationFillMode: 'forwards',\n                            lineHeight: '1.1',\n                            letterSpacing: '-0.03em'\n                        },\n                        children: [\n                            \"BALI YOGA\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                lineNumber: 35,\n                                columnNumber: 20\n                            }, undefined),\n                            \"JOURNEY\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-body text-ink/70 mb-16 opacity-0 animate-fade-in max-w-md mx-auto\",\n                        style: {\n                            animationDelay: '1.2s',\n                            animationFillMode: 'forwards'\n                        },\n                        children: \"Transform your practice in paradise\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"#luxury-experience\",\n                        className: \"inline-block bg-temple-gold text-silk px-12 py-4 text-sm font-light tracking-[0.1em] uppercase transition-all duration-400 hover:opacity-70 opacity-0 animate-fade-in\",\n                        style: {\n                            animationDelay: '1.6s',\n                            animationFillMode: 'forwards'\n                        },\n                        \"aria-label\": \"Begin your luxury yoga journey\",\n                        children: \"Begin Journey\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n// Usunieto komponent Card - nie potrzebny w ultra minimalistycznym designie\n// Usunieto wszystkie niepotrzebne dane dla ultra minimalizmu\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative bg-silk\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroSection, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"luxury-experience\",\n                className: \"py-48 md:py-64 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-luxury mx-auto px-[16.67%]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-12 gap-16 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-micro uppercase tracking-[0.05em] text-ink/60 mb-8\",\n                                        children: \"Our Destinations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-h2 font-extralight text-ink mb-12\",\n                                        children: [\n                                            \"Curated\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 24\n                                            }, this),\n                                            \"Experiences\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-body text-ink/70 mb-8\",\n                                        children: \"Immerse yourself in the sacred landscapes of Bali and Sri Lanka, where ancient wisdom meets modern luxury.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-body text-ink/70\",\n                                        children: \"Each retreat is meticulously designed for transformation through mindful practice and cultural immersion.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-6 col-start-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-[600px] bg-sand\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/images/blog/temple-bali.webp\",\n                                        alt: \"Luxury yoga retreat in Bali\",\n                                        fill: true,\n                                        className: \"object-cover\",\n                                        sizes: \"50vw\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-48 md:py-64 bg-sand relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-luxury mx-auto px-[16.67%]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-12 gap-16 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-[600px] bg-silk\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/images/blog/handstand-828.webp\",\n                                        alt: \"Yoga practice philosophy\",\n                                        fill: true,\n                                        className: \"object-cover\",\n                                        sizes: \"50vw\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-4 col-start-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-micro uppercase tracking-[0.05em] text-ink/60 mb-8\",\n                                        children: \"Our Philosophy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-h2 font-extralight text-ink mb-12\",\n                                        children: [\n                                            \"Mindful\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 24\n                                            }, this),\n                                            \"Transformation\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-body text-ink/70 mb-8\",\n                                        children: \"Beyond physical practice lies a deeper journey of self-discovery and inner awakening.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-body text-ink/70\",\n                                        children: \"Our approach integrates traditional yoga wisdom with contemporary wellness practices.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-48 md:py-64 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-luxury mx-auto px-[16.67%] text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-micro uppercase tracking-[0.05em] text-ink/60 mb-16\",\n                            children: \"Trusted by\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-16 mb-24\",\n                            children: [\n                                {\n                                    name: 'Marta K.',\n                                    title: 'Architect',\n                                    location: 'Warsaw'\n                                },\n                                {\n                                    name: 'Tomasz W.',\n                                    title: 'Designer',\n                                    location: 'Krakow'\n                                },\n                                {\n                                    name: 'Anna M.',\n                                    title: 'Entrepreneur',\n                                    location: 'Wroclaw'\n                                }\n                            ].map((person, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-sand rounded-full mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-body font-light text-ink mb-1\",\n                                            children: person.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-micro uppercase tracking-[0.05em] text-ink/60\",\n                                            children: person.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-48 md:py-64 bg-sand relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-luxury mx-auto px-[16.67%] text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-h2 font-extralight text-ink mb-16\",\n                            children: [\n                                \"Ready to\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 21\n                                }, this),\n                                \"Transform?\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-body text-ink/70 mb-24 max-w-md mx-auto\",\n                            children: \"Join our next exclusive retreat and discover the profound connection between mind, body, and spirit.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/kontakt\",\n                            className: \"inline-block bg-temple-gold text-silk px-16 py-5 text-sm font-light tracking-[0.1em] uppercase transition-all duration-400 hover:opacity-70\",\n                            \"aria-label\": \"Reserve your space\",\n                            children: \"Reserve Your Space\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\page.jsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.jsx\n");

/***/ }),

/***/ "(rsc)/./src/components/AsyncCSS.jsx":
/*!*************************************!*\
  !*** ./src/components/AsyncCSS.jsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\AsyncCSS.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_template\\my-travel-blog\\src\\components\\AsyncCSS.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ClientAnalytics.jsx":
/*!********************************************!*\
  !*** ./src/components/ClientAnalytics.jsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ClientAnalytics: () => (/* binding */ ClientAnalytics)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ClientAnalytics = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ClientAnalytics() from the server but ClientAnalytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_template\\my-travel-blog\\src\\components\\ClientAnalytics.jsx",
"ClientAnalytics",
);

/***/ }),

/***/ "(rsc)/./src/components/ClientResourcePreloader.jsx":
/*!****************************************************!*\
  !*** ./src/components/ClientResourcePreloader.jsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\ClientResourcePreloader.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_template\\my-travel-blog\\src\\components\\ClientResourcePreloader.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/CookieConsent.jsx":
/*!******************************************!*\
  !*** ./src/components/CookieConsent.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\CookieConsent.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_template\\my-travel-blog\\src\\components\\CookieConsent.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/CriticalCSS.jsx":
/*!****************************************!*\
  !*** ./src/components/CriticalCSS.jsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   criticalStyles: () => (/* binding */ criticalStyles),\n/* harmony export */   \"default\": () => (/* binding */ CriticalCSS)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n// Critical CSS for above-the-fold content - only essential styles\n\nconst criticalStyles = `\n  /* CSS Variables - Critical colors only */\n  :root {\n    --color-primary: 26 26 26;\n    --color-secondary: 248 246 243;\n    --color-accent: 139 115 85;\n    --color-soft-sage: 232 229 224;\n    --color-warm-gold: 212 165 116;\n    --font-montserrat: 'Montserrat', 'system-ui', sans-serif;\n    --font-cormorant: 'Cormorant Garamond', 'Georgia', serif;\n  }\n\n  /* Critical base styles */\n  * { box-sizing: border-box; }\n  html { scroll-behavior: smooth; -webkit-font-smoothing: antialiased; }\n  body {\n    font-family: var(--font-montserrat);\n    color: rgb(var(--color-primary));\n    background: rgb(var(--color-secondary));\n    margin: 0;\n    line-height: 1.75;\n    overflow-x: hidden;\n  }\n\n  /* Critical layout */\n  .min-h-screen { min-height: 100vh; }\n  .flex { display: flex; }\n  .flex-col { flex-direction: column; }\n  .items-center { align-items: center; }\n  .justify-center { justify-content: center; }\n  .text-center { text-align: center; }\n  .relative { position: relative; }\n  .absolute { position: absolute; }\n  .inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\n  .w-full { width: 100%; }\n  .h-full { height: 100%; }\n  .object-cover { object-fit: cover; }\n  .overflow-hidden { overflow: hidden; }\n  .z-10 { z-index: 10; }\n\n  /* Critical typography */\n  .font-serif { font-family: var(--font-playfair), serif; }\n  .text-4xl { font-size: 2.25rem; line-height: 2.5rem; }\n  .text-5xl { font-size: 3rem; line-height: 1; }\n  .text-6xl { font-size: 3.75rem; line-height: 1; }\n  .text-lg { font-size: 1.125rem; line-height: 1.75rem; }\n  .text-xl { font-size: 1.25rem; line-height: 1.75rem; }\n  .leading-tight { line-height: 1.25; }\n  .leading-relaxed { line-height: 1.625; }\n  .tracking-tight { letter-spacing: -0.025em; }\n  .drop-shadow-md { filter: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06)); }\n\n  /* Critical colors */\n  .text-primary { color: rgb(var(--color-primary)); }\n  .text-accent { color: rgb(var(--color-accent)); }\n  .bg-primary { background-color: rgb(var(--color-primary)); }\n  .bg-secondary { background-color: rgb(var(--color-secondary)); }\n\n  /* Critical spacing */\n  .p-4 { padding: 1rem; }\n  .p-6 { padding: 1.5rem; }\n  .px-4 { padding-left: 1rem; padding-right: 1rem; }\n  .px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }\n  .px-8 { padding-left: 2rem; padding-right: 2rem; }\n  .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }\n  .py-20 { padding-top: 5rem; padding-bottom: 5rem; }\n  .mb-4 { margin-bottom: 1rem; }\n  .mb-6 { margin-bottom: 1.5rem; }\n  .mb-8 { margin-bottom: 2rem; }\n  .mt-4 { margin-top: 1rem; }\n  .mt-6 { margin-top: 1.5rem; }\n  .mx-auto { margin-left: auto; margin-right: auto; }\n  .max-w-2xl { max-width: 42rem; }\n  .max-w-5xl { max-width: 64rem; }\n\n  /* Critical responsive */\n  @media (min-width: 640px) {\n    .sm\\\\:text-5xl { font-size: 3rem; line-height: 1; }\n    .sm\\\\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }\n    .sm\\\\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }\n  }\n\n  @media (min-width: 768px) {\n    .md\\\\:min-h-\\\\[90vh\\\\] { min-height: 90vh; }\n  }\n\n  @media (min-width: 1024px) {\n    .lg\\\\:text-6xl { font-size: 3.75rem; line-height: 1; }\n    .lg\\\\:px-8 { padding-left: 2rem; padding-right: 2rem; }\n  }\n\n  /* Critical hero styles */\n  .glass-effect::before {\n    content: '';\n    position: absolute;\n    inset: 0;\n    background: linear-gradient(135deg, rgb(var(--color-shell) / 0.1), rgb(var(--color-mist) / 0.05));\n  }\n\n  /* Critical button base */\n  .btn-primary {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    padding: 0.75rem 2rem;\n    font-size: 0.875rem;\n    font-weight: 400;\n    text-transform: uppercase;\n    letter-spacing: 0.1em;\n    color: rgb(var(--color-primary));\n    background: transparent;\n    border: 1px solid rgb(var(--color-primary));\n    transition: all 0.3s ease;\n    cursor: pointer;\n    text-decoration: none;\n  }\n\n  .btn-primary:hover {\n    background: rgb(var(--color-primary));\n    color: rgb(var(--color-secondary));\n  }\n`;\nfunction CriticalCSS() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n        dangerouslySetInnerHTML: {\n            __html: criticalStyles\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\CriticalCSS.jsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/CriticalCSS.jsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Footer/ServerFooter.jsx":
/*!************************************************!*\
  !*** ./src/components/Footer/ServerFooter.jsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServerFooter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction ServerFooter() {\n    const currentYear = new Date().getFullYear();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        id: \"footer\",\n        className: \"pt-48 pb-12 bg-silk\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-luxury mx-auto px-[16.67%]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-h2 font-extralight text-ink mb-8 tracking-[0.2em]\",\n                            children: \"BAKASANA\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Footer\\\\ServerFooter.jsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-body text-ink/60 font-light\",\n                            children: \"Luxury yoga retreats\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Footer\\\\ServerFooter.jsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Footer\\\\ServerFooter.jsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-ink/10 pt-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-micro text-ink/40 font-light tracking-[0.1em] uppercase\",\n                        children: [\n                            \"\\xa9 \",\n                            currentYear,\n                            \" Bakasana\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Footer\\\\ServerFooter.jsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Footer\\\\ServerFooter.jsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Footer\\\\ServerFooter.jsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Footer\\\\ServerFooter.jsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Footer/ServerFooter.jsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Navbar/ServerNavbar.jsx":
/*!************************************************!*\
  !*** ./src/components/Navbar/ServerNavbar.jsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServerNavbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Static navigation links to avoid import issues\nconst staticNavLinks = [\n    {\n        href: '/',\n        label: 'Strona główna'\n    },\n    {\n        href: '/blog',\n        label: 'Blog'\n    },\n    {\n        href: '/program',\n        label: 'Program',\n        submenu: [\n            {\n                href: '/program?destination=bali',\n                label: 'Bali - 12 dni'\n            },\n            {\n                href: '/program?destination=srilanka',\n                label: 'Sri Lanka - 10 dni'\n            }\n        ]\n    },\n    {\n        href: '/zajecia-online',\n        label: 'Zajęcia Online'\n    },\n    {\n        href: '/o-mnie',\n        label: 'O mnie'\n    },\n    {\n        href: '/galeria',\n        label: 'Galeria'\n    },\n    {\n        href: '/kontakt',\n        label: 'Kontakt'\n    }\n];\nfunction ServerNavbar() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n            className: \"bg-silk/95 backdrop-blur-md border-b border-ink/5 transition-all duration-500 h-25\",\n            style: {\n                height: '100px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-luxury mx-auto px-[16.67%] h-full\",\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/\",\n                                className: \"flex items-center group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-sans font-extralight text-ink tracking-[0.2em] transition-all duration-500 group-hover:opacity-70\",\n                                    children: \"BAKASANA\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Navbar\\\\ServerNavbar.jsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Navbar\\\\ServerNavbar.jsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block relative group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"opacity-0 group-hover:opacity-100 transition-all duration-500 absolute right-0 top-full mt-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"flex flex-col space-y-4 bg-silk/95 backdrop-blur-md shadow-luxury p-8 min-w-[200px]\",\n                                        children: staticNavLinks.filter((link)=>!link.submenu).map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: link.href,\n                                                className: \"text-sm font-light text-ink/70 hover:opacity-70 transition-all duration-400 tracking-[0.1em] uppercase\",\n                                                children: link.label\n                                            }, link.href, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Navbar\\\\ServerNavbar.jsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Navbar\\\\ServerNavbar.jsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Navbar\\\\ServerNavbar.jsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Navbar\\\\ServerNavbar.jsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/kontakt\",\n                                className: \"bg-temple-gold text-silk px-8 py-3 text-sm font-light tracking-[0.1em] uppercase transition-all duration-400 hover:opacity-70\",\n                                children: \"Book Now\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Navbar\\\\ServerNavbar.jsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                            className: \"text-ink cursor-pointer font-light text-sm uppercase tracking-[0.1em]\",\n                                            children: \"Menu\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Navbar\\\\ServerNavbar.jsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"absolute right-0 top-full mt-2 bg-silk/95 backdrop-blur-md shadow-luxury p-6 min-w-[200px]\",\n                                            children: [\n                                                staticNavLinks.filter((link)=>!link.submenu).map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: link.href,\n                                                        className: \"block text-sm font-light text-ink/70 hover:opacity-70 transition-all duration-400 py-3 uppercase tracking-[0.1em]\",\n                                                        children: link.label\n                                                    }, link.href, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Navbar\\\\ServerNavbar.jsx\",\n                                                        lineNumber: 67,\n                                                        columnNumber: 21\n                                                    }, this)),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 pt-4 border-t border-ink/10\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/kontakt\",\n                                                        className: \"block text-sm font-light text-temple-gold py-2 uppercase tracking-[0.1em]\",\n                                                        children: \"Book Now\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Navbar\\\\ServerNavbar.jsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Navbar\\\\ServerNavbar.jsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Navbar\\\\ServerNavbar.jsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Navbar\\\\ServerNavbar.jsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Navbar\\\\ServerNavbar.jsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Navbar\\\\ServerNavbar.jsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Navbar\\\\ServerNavbar.jsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\Navbar\\\\ServerNavbar.jsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Navbar/ServerNavbar.jsx\n");

/***/ }),

/***/ "(rsc)/./src/components/PWAInstaller.jsx":
/*!*****************************************!*\
  !*** ./src/components/PWAInstaller.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PWAFeatures: () => (/* binding */ PWAFeatures),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   usePWAInstall: () => (/* binding */ usePWAInstall)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_template\\my-travel-blog\\src\\components\\PWAInstaller.jsx",
"default",
));
const usePWAInstall = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call usePWAInstall() from the server but usePWAInstall is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_template\\my-travel-blog\\src\\components\\PWAInstaller.jsx",
"usePWAInstall",
);const PWAFeatures = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call PWAFeatures() from the server but PWAFeatures is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_template\\my-travel-blog\\src\\components\\PWAInstaller.jsx",
"PWAFeatures",
);

/***/ }),

/***/ "(rsc)/./src/components/SmoothScroll.jsx":
/*!*****************************************!*\
  !*** ./src/components/SmoothScroll.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\SmoothScroll.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_template\\my-travel-blog\\src\\components\\SmoothScroll.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/WebVitalsMonitor.jsx":
/*!*********************************************!*\
  !*** ./src/components/WebVitalsMonitor.jsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\WebVitalsMonitor.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_template\\my-travel-blog\\src\\components\\WebVitalsMonitor.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/lib/structuredData.js":
/*!***********************************!*\
  !*** ./src/lib/structuredData.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateArticleSchema: () => (/* binding */ generateArticleSchema),\n/* harmony export */   generateBreadcrumbSchema: () => (/* binding */ generateBreadcrumbSchema),\n/* harmony export */   generateEventSchema: () => (/* binding */ generateEventSchema),\n/* harmony export */   generateFAQSchema: () => (/* binding */ generateFAQSchema),\n/* harmony export */   generateLocalBusinessSchema: () => (/* binding */ generateLocalBusinessSchema),\n/* harmony export */   generateOrganizationSchema: () => (/* binding */ generateOrganizationSchema),\n/* harmony export */   generateProductSchema: () => (/* binding */ generateProductSchema),\n/* harmony export */   generateReviewSchema: () => (/* binding */ generateReviewSchema),\n/* harmony export */   generateServiceSchema: () => (/* binding */ generateServiceSchema),\n/* harmony export */   generateWebsiteSchema: () => (/* binding */ generateWebsiteSchema)\n/* harmony export */ });\n// Advanced Structured Data for SEO\nfunction generateOrganizationSchema() {\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Organization\",\n        \"name\": \"Bali Yoga Journey\",\n        \"alternateName\": \"Fly with Bakasana\",\n        \"url\": \"https://bakasana-travel.blog\",\n        \"logo\": \"https://bakasana-travel.blog/apple-touch-icon.png\",\n        \"description\": \"Retreaty jogowe na Bali z polską instruktorką Julią Jakubowicz. Małe grupy, all inclusive, niezapomniane doświadczenia.\",\n        \"founder\": {\n            \"@type\": \"Person\",\n            \"name\": \"Julia Jakubowicz\",\n            \"jobTitle\": \"Instruktorka Jogi\",\n            \"description\": \"Certyfikowana instruktorka jogi z wieloletnim doświadczeniem, specjalizująca się w retreatach na Bali.\",\n            \"image\": \"https://bakasana-travel.blog/images/julia-profile.webp\",\n            \"sameAs\": [\n                \"https://www.instagram.com/fly_with_bakasana/\",\n                \"https://www.facebook.com/flywithbakasana\"\n            ]\n        },\n        \"contactPoint\": {\n            \"@type\": \"ContactPoint\",\n            \"telephone\": \"+**************\",\n            \"contactType\": \"customer service\",\n            \"email\": \"<EMAIL>\",\n            \"availableLanguage\": [\n                \"Polish\",\n                \"English\"\n            ]\n        },\n        \"address\": {\n            \"@type\": \"PostalAddress\",\n            \"addressLocality\": \"Rzeszów\",\n            \"addressCountry\": \"PL\"\n        },\n        \"sameAs\": [\n            \"https://www.instagram.com/fly_with_bakasana/\",\n            \"https://www.facebook.com/flywithbakasana\"\n        ],\n        \"areaServed\": {\n            \"@type\": \"Country\",\n            \"name\": \"Poland\"\n        },\n        \"serviceType\": [\n            \"Yoga Retreats\",\n            \"Travel Services\",\n            \"Wellness Tourism\",\n            \"Yoga Instruction\"\n        ]\n    };\n}\nfunction generateWebsiteSchema() {\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"WebSite\",\n        \"name\": \"Bali Yoga Journey\",\n        \"url\": \"https://bakasana-travel.blog\",\n        \"description\": \"Retreaty jogowe na Bali z polską instruktorką. Odkryj magię Bali podczas niezapomnianych wyjazdów jogowych.\",\n        \"inLanguage\": \"pl-PL\",\n        \"potentialAction\": {\n            \"@type\": \"SearchAction\",\n            \"target\": \"https://bakasana-travel.blog/search?q={search_term_string}\",\n            \"query-input\": \"required name=search_term_string\"\n        },\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"Bali Yoga Journey\",\n            \"logo\": {\n                \"@type\": \"ImageObject\",\n                \"url\": \"https://bakasana-travel.blog/apple-touch-icon.png\"\n            }\n        }\n    };\n}\nfunction generateServiceSchema() {\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Service\",\n        \"name\": \"Retreaty Jogowe na Bali\",\n        \"description\": \"Profesjonalnie organizowane retreaty jogowe na Bali z polską instruktorką. Małe grupy, all inclusive, transport, zakwaterowanie i wszystkie posiłki wliczone.\",\n        \"provider\": {\n            \"@type\": \"Organization\",\n            \"name\": \"Bali Yoga Journey\"\n        },\n        \"areaServed\": {\n            \"@type\": \"Place\",\n            \"name\": \"Bali, Indonesia\"\n        },\n        \"serviceType\": \"Yoga Retreat\",\n        \"offers\": {\n            \"@type\": \"Offer\",\n            \"priceRange\": \"2900-3400 PLN\",\n            \"priceCurrency\": \"PLN\",\n            \"availability\": \"https://schema.org/InStock\",\n            \"validFrom\": \"2025-01-01\",\n            \"validThrough\": \"2025-12-31\"\n        },\n        \"hasOfferCatalog\": {\n            \"@type\": \"OfferCatalog\",\n            \"name\": \"Retreaty Jogowe 2025\",\n            \"itemListElement\": [\n                {\n                    \"@type\": \"Offer\",\n                    \"itemOffered\": {\n                        \"@type\": \"Service\",\n                        \"name\": \"Wiosenny Retreat Jogowy - Marzec 2025\",\n                        \"description\": \"7-dniowy retreat jogowy na Bali z praktyką na plażach Uluwatu\"\n                    },\n                    \"price\": \"2900\",\n                    \"priceCurrency\": \"PLN\"\n                },\n                {\n                    \"@type\": \"Offer\",\n                    \"itemOffered\": {\n                        \"@type\": \"Service\",\n                        \"name\": \"Majowy Retreat Jogowy - Maj 2025\",\n                        \"description\": \"7-dniowy retreat z fokusem na advanced asany i medytację\"\n                    },\n                    \"price\": \"3200\",\n                    \"priceCurrency\": \"PLN\"\n                }\n            ]\n        }\n    };\n}\nfunction generateBreadcrumbSchema(breadcrumbs) {\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"BreadcrumbList\",\n        \"itemListElement\": breadcrumbs.map((crumb, index)=>({\n                \"@type\": \"ListItem\",\n                \"position\": index + 1,\n                \"name\": crumb.name,\n                \"item\": `https://bakasana-travel.blog${crumb.url}`\n            }))\n    };\n}\nfunction generateFAQSchema(faqs) {\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"FAQPage\",\n        \"mainEntity\": faqs.map((faq)=>({\n                \"@type\": \"Question\",\n                \"name\": faq.question,\n                \"acceptedAnswer\": {\n                    \"@type\": \"Answer\",\n                    \"text\": faq.answer\n                }\n            }))\n    };\n}\nfunction generateArticleSchema(article) {\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Article\",\n        \"headline\": article.title,\n        \"description\": article.excerpt,\n        \"image\": article.image ? `https://bakasana-travel.blog${article.image}` : \"https://bakasana-travel.blog/apple-touch-icon.png\",\n        \"author\": {\n            \"@type\": \"Person\",\n            \"name\": \"Julia Jakubowicz\",\n            \"url\": \"https://bakasana-travel.blog/o-mnie\"\n        },\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"Bali Yoga Journey\",\n            \"logo\": {\n                \"@type\": \"ImageObject\",\n                \"url\": \"https://bakasana-travel.blog/apple-touch-icon.png\"\n            }\n        },\n        \"datePublished\": article.date,\n        \"dateModified\": article.dateModified || article.date,\n        \"mainEntityOfPage\": {\n            \"@type\": \"WebPage\",\n            \"@id\": `https://bakasana-travel.blog/blog/${article.slug}`\n        },\n        \"articleSection\": article.category,\n        \"keywords\": article.keywords || [],\n        \"wordCount\": article.wordCount || 1000,\n        \"inLanguage\": \"pl-PL\"\n    };\n}\nfunction generateEventSchema(retreat) {\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Event\",\n        \"name\": retreat.title,\n        \"description\": retreat.description,\n        \"startDate\": retreat.startDate,\n        \"endDate\": retreat.endDate,\n        \"eventStatus\": \"https://schema.org/EventScheduled\",\n        \"eventAttendanceMode\": \"https://schema.org/OfflineEventAttendanceMode\",\n        \"location\": {\n            \"@type\": \"Place\",\n            \"name\": \"Bali, Indonesia\",\n            \"address\": {\n                \"@type\": \"PostalAddress\",\n                \"addressLocality\": \"Bali\",\n                \"addressCountry\": \"ID\"\n            }\n        },\n        \"organizer\": {\n            \"@type\": \"Organization\",\n            \"name\": \"Bali Yoga Journey\",\n            \"url\": \"https://bakasana-travel.blog\"\n        },\n        \"offers\": {\n            \"@type\": \"Offer\",\n            \"url\": \"https://bakasana-travel.blog/rezerwacja\",\n            \"price\": retreat.price,\n            \"priceCurrency\": \"PLN\",\n            \"availability\": retreat.spotsLeft > 0 ? \"https://schema.org/InStock\" : \"https://schema.org/SoldOut\",\n            \"validFrom\": \"2025-01-01\"\n        },\n        \"performer\": {\n            \"@type\": \"Person\",\n            \"name\": \"Julia Jakubowicz\",\n            \"description\": \"Certyfikowana instruktorka jogi\"\n        },\n        \"maximumAttendeeCapacity\": 12,\n        \"remainingAttendeeCapacity\": retreat.spotsLeft || 0\n    };\n}\nfunction generateLocalBusinessSchema() {\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"TravelAgency\",\n        \"name\": \"Bali Yoga Journey\",\n        \"image\": \"https://bakasana-travel.blog/apple-touch-icon.png\",\n        \"description\": \"Specjalizujemy się w organizacji retreatów jogowych na Bali. Oferujemy kompleksową obsługę: transport, zakwaterowanie, wyżywienie i profesjonalną instrukcję jogi.\",\n        \"address\": {\n            \"@type\": \"PostalAddress\",\n            \"addressLocality\": \"Rzeszów\",\n            \"addressCountry\": \"PL\"\n        },\n        \"geo\": {\n            \"@type\": \"GeoCoordinates\",\n            \"latitude\": 50.0412,\n            \"longitude\": 21.9991\n        },\n        \"url\": \"https://bakasana-travel.blog\",\n        \"telephone\": \"+**************\",\n        \"email\": \"<EMAIL>\",\n        \"priceRange\": \"2900-3400 PLN\",\n        \"openingHours\": \"Mo-Su 09:00-18:00\",\n        \"paymentAccepted\": [\n            \"Bank Transfer\",\n            \"BLIK\"\n        ],\n        \"currenciesAccepted\": \"PLN\",\n        \"areaServed\": [\n            {\n                \"@type\": \"Country\",\n                \"name\": \"Poland\"\n            }\n        ],\n        \"serviceArea\": {\n            \"@type\": \"Place\",\n            \"name\": \"Bali, Indonesia\"\n        },\n        \"hasOfferCatalog\": {\n            \"@type\": \"OfferCatalog\",\n            \"name\": \"Retreaty Jogowe na Bali\",\n            \"itemListElement\": [\n                {\n                    \"@type\": \"Offer\",\n                    \"itemOffered\": {\n                        \"@type\": \"Trip\",\n                        \"name\": \"7-dniowy Retreat Jogowy na Bali\",\n                        \"description\": \"Kompleksowy pakiet: zakwaterowanie, wyżywienie, transport, joga 2x dziennie\"\n                    }\n                }\n            ]\n        }\n    };\n}\nfunction generateReviewSchema(reviews) {\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Organization\",\n        \"name\": \"Bali Yoga Journey\",\n        \"aggregateRating\": {\n            \"@type\": \"AggregateRating\",\n            \"ratingValue\": \"4.9\",\n            \"reviewCount\": reviews.length,\n            \"bestRating\": \"5\",\n            \"worstRating\": \"1\"\n        },\n        \"review\": reviews.map((review)=>({\n                \"@type\": \"Review\",\n                \"author\": {\n                    \"@type\": \"Person\",\n                    \"name\": review.author\n                },\n                \"reviewRating\": {\n                    \"@type\": \"Rating\",\n                    \"ratingValue\": review.rating,\n                    \"bestRating\": \"5\"\n                },\n                \"reviewBody\": review.text,\n                \"datePublished\": review.date\n            }))\n    };\n}\nfunction generateProductSchema(retreat) {\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Product\",\n        \"name\": retreat.title,\n        \"description\": retreat.description,\n        \"image\": retreat.image ? `https://bakasana-travel.blog${retreat.image}` : \"https://bakasana-travel.blog/apple-touch-icon.png\",\n        \"brand\": {\n            \"@type\": \"Brand\",\n            \"name\": \"Bali Yoga Journey\"\n        },\n        \"offers\": {\n            \"@type\": \"Offer\",\n            \"url\": \"https://bakasana-travel.blog/rezerwacja\",\n            \"priceCurrency\": \"PLN\",\n            \"price\": retreat.price,\n            \"priceValidUntil\": \"2025-12-31\",\n            \"availability\": retreat.spotsLeft > 0 ? \"https://schema.org/InStock\" : \"https://schema.org/SoldOut\",\n            \"seller\": {\n                \"@type\": \"Organization\",\n                \"name\": \"Bali Yoga Journey\"\n            }\n        },\n        \"aggregateRating\": {\n            \"@type\": \"AggregateRating\",\n            \"ratingValue\": \"4.9\",\n            \"reviewCount\": \"47\",\n            \"bestRating\": \"5\"\n        },\n        \"category\": \"Travel Package\"\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/structuredData.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5C%40vercel%5C%5Canalytics%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5C%40vercel%5C%5Cspeed-insights%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.jsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Montserrat%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CAsyncCSS.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CClientAnalytics.jsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CClientResourcePreloader.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CCookieConsent.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CSmoothScroll.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CWebVitalsMonitor.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5C%40vercel%5C%5Canalytics%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5C%40vercel%5C%5Cspeed-insights%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.jsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Montserrat%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CAsyncCSS.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CClientAnalytics.jsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CClientResourcePreloader.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CCookieConsent.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CSmoothScroll.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CWebVitalsMonitor.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@vercel/analytics/dist/react/index.mjs */ \"(ssr)/./node_modules/@vercel/analytics/dist/react/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@vercel/speed-insights/dist/react/index.mjs */ \"(ssr)/./node_modules/@vercel/speed-insights/dist/react/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AsyncCSS.jsx */ \"(ssr)/./src/components/AsyncCSS.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientAnalytics.jsx */ \"(ssr)/./src/components/ClientAnalytics.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientResourcePreloader.jsx */ \"(ssr)/./src/components/ClientResourcePreloader.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/CookieConsent.jsx */ \"(ssr)/./src/components/CookieConsent.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PWAInstaller.jsx */ \"(ssr)/./src/components/PWAInstaller.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SmoothScroll.jsx */ \"(ssr)/./src/components/SmoothScroll.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/WebVitalsMonitor.jsx */ \"(ssr)/./src/components/WebVitalsMonitor.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5C%40vercel%5C%5Canalytics%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5C%40vercel%5C%5Cspeed-insights%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.jsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Montserrat%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CAsyncCSS.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CClientAnalytics.jsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CClientResourcePreloader.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CCookieConsent.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CSmoothScroll.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Ccomponents%5C%5CWebVitalsMonitor.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2RhdmlkJTVDJTVDRGVza3RvcCU1QyU1Q1Byb2pla3R5JTVDJTVDYmxvZ190ZW1wbGF0ZSU1QyU1Q215LXRyYXZlbC1ibG9nJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNhcHAtZGlyJTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMl9fZXNNb2R1bGUlMjIlMkMlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2RhdmlkJTVDJTVDRGVza3RvcCU1QyU1Q1Byb2pla3R5JTVDJTVDYmxvZ190ZW1wbGF0ZSU1QyU1Q215LXRyYXZlbC1ibG9nJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNpbWFnZS1jb21wb25lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdOQUFxTTtBQUNyTTtBQUNBLHNOQUE2SiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiX19lc01vZHVsZVwiLFwiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGRhdmlkXFxcXERlc2t0b3BcXFxcUHJvamVrdHlcXFxcYmxvZ190ZW1wbGF0ZVxcXFxteS10cmF2ZWwtYmxvZ1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxhcHAtZGlyXFxcXGxpbmsuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGRhdmlkXFxcXERlc2t0b3BcXFxcUHJvamVrdHlcXFxcYmxvZ190ZW1wbGF0ZVxcXFxteS10cmF2ZWwtYmxvZ1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxpbWFnZS1jb21wb25lbnQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2RhdmlkJTVDJTVDRGVza3RvcCU1QyU1Q1Byb2pla3R5JTVDJTVDYmxvZ190ZW1wbGF0ZSU1QyU1Q215LXRyYXZlbC1ibG9nJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNhcHAtZGlyJTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMl9fZXNNb2R1bGUlMjIlMkMlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTkFBcU0iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIl9fZXNNb2R1bGVcIixcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxkYXZpZFxcXFxEZXNrdG9wXFxcXFByb2pla3R5XFxcXGJsb2dfdGVtcGxhdGVcXFxcbXktdHJhdmVsLWJsb2dcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcYXBwLWRpclxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Capp%5C%5Cerror.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Capp%5C%5Cerror.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.jsx */ \"(ssr)/./src/app/error.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2RhdmlkJTVDJTVDRGVza3RvcCU1QyU1Q1Byb2pla3R5JTVDJTVDYmxvZ190ZW1wbGF0ZSU1QyU1Q215LXRyYXZlbC1ibG9nJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZXJyb3IuanN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrSkFBNEgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGRhdmlkXFxcXERlc2t0b3BcXFxcUHJvamVrdHlcXFxcYmxvZ190ZW1wbGF0ZVxcXFxteS10cmF2ZWwtYmxvZ1xcXFxzcmNcXFxcYXBwXFxcXGVycm9yLmpzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cblog_template%5C%5Cmy-travel-blog%5C%5Csrc%5C%5Capp%5C%5Cerror.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/error.jsx":
/*!***************************!*\
  !*** ./src/app/error.jsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Error.useEffect\": ()=>{\n            // Bezpieczne logowanie błędu do konsoli\n            try {\n                if (error) {\n                    console.warn(\"--- Błąd Aplikacji Next.js ---\");\n                    if (error.message) {\n                        console.warn(\"Wiadomość:\", error.message);\n                    }\n                    if (error.stack) {\n                        console.warn(\"Stos wywołań:\", error.stack);\n                    }\n                }\n            } catch (loggingError) {\n            // Ignoruj błędy logowania\n            }\n        }\n    }[\"Error.useEffect\"], [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"py-32 min-h-screen bg-gradient-to-b from-rice via-sand-light/50 to-ocean/10 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto px-4 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-5xl md:text-6xl font-display text-temple tracking-tight mb-6\",\n                    children: \"Ups!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\error.jsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl md:text-3xl font-display text-temple mb-4\",\n                    children: \"Coś poszło nie tak\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\error.jsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg text-wood-light/80 mb-8\",\n                    children: \"Przepraszamy, wystąpił nieoczekiwany błąd. Nasz zesp\\xf3ł został powiadomiony.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\error.jsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>reset(),\n                            className: \"inline-flex items-center justify-center px-6 py-3 bg-temple text-rice rounded-full hover:bg-temple/90 transition-colors\",\n                            children: \"Spr\\xf3buj ponownie\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\error.jsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"inline-flex items-center justify-center px-6 py-3 bg-rice text-temple border border-temple/20 rounded-full hover:bg-rice/80 transition-colors\",\n                            children: \"Wr\\xf3ć na stronę gł\\xf3wną\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\error.jsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\error.jsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\error.jsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\app\\\\error.jsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Vycm9yLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUVrQztBQUNMO0FBRWQsU0FBU0UsTUFBTSxFQUFFQyxLQUFLLEVBQUVDLEtBQUssRUFBRTtJQUM1Q0osZ0RBQVNBOzJCQUFDO1lBQ1Isd0NBQXdDO1lBQ3hDLElBQUk7Z0JBQ0YsSUFBSUcsT0FBTztvQkFDVEUsUUFBUUMsSUFBSSxDQUFDO29CQUNiLElBQUlILE1BQU1JLE9BQU8sRUFBRTt3QkFDakJGLFFBQVFDLElBQUksQ0FBQyxjQUFjSCxNQUFNSSxPQUFPO29CQUMxQztvQkFDQSxJQUFJSixNQUFNSyxLQUFLLEVBQUU7d0JBQ2ZILFFBQVFDLElBQUksQ0FBQyxpQkFBaUJILE1BQU1LLEtBQUs7b0JBQzNDO2dCQUNGO1lBQ0YsRUFBRSxPQUFPQyxjQUFjO1lBQ3JCLDBCQUEwQjtZQUM1QjtRQUNGOzBCQUFHO1FBQUNOO0tBQU07SUFFVixxQkFDRSw4REFBQ087UUFBS0MsV0FBVTtrQkFDZCw0RUFBQ0M7WUFBSUQsV0FBVTs7OEJBQ2IsOERBQUNFO29CQUFHRixXQUFVOzhCQUFvRTs7Ozs7OzhCQUdsRiw4REFBQ0c7b0JBQUdILFdBQVU7OEJBQXFEOzs7Ozs7OEJBR25FLDhEQUFDSTtvQkFBRUosV0FBVTs4QkFBa0M7Ozs7Ozs4QkFHL0MsOERBQUNDO29CQUFJRCxXQUFVOztzQ0FDYiw4REFBQ0s7NEJBQ0NDLFNBQVMsSUFBTWI7NEJBQ2ZPLFdBQVU7c0NBQ1g7Ozs7OztzQ0FHRCw4REFBQ1Ysa0RBQUlBOzRCQUNIaUIsTUFBSzs0QkFDTFAsV0FBVTtzQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPWCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkYXZpZFxcRGVza3RvcFxcUHJvamVrdHlcXGJsb2dfdGVtcGxhdGVcXG15LXRyYXZlbC1ibG9nXFxzcmNcXGFwcFxcZXJyb3IuanN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEVycm9yKHsgZXJyb3IsIHJlc2V0IH0pIHtcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgLy8gQmV6cGllY3puZSBsb2dvd2FuaWUgYsWCxJlkdSBkbyBrb25zb2xpXHJcbiAgICB0cnkge1xyXG4gICAgICBpZiAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLndhcm4oXCItLS0gQsWCxIVkIEFwbGlrYWNqaSBOZXh0LmpzIC0tLVwiKTtcclxuICAgICAgICBpZiAoZXJyb3IubWVzc2FnZSkge1xyXG4gICAgICAgICAgY29uc29sZS53YXJuKFwiV2lhZG9tb8WbxIc6XCIsIGVycm9yLm1lc3NhZ2UpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoZXJyb3Iuc3RhY2spIHtcclxuICAgICAgICAgIGNvbnNvbGUud2FybihcIlN0b3Mgd3l3b8WCYcWEOlwiLCBlcnJvci5zdGFjayk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChsb2dnaW5nRXJyb3IpIHtcclxuICAgICAgLy8gSWdub3J1aiBixYLEmWR5IGxvZ293YW5pYVxyXG4gICAgfVxyXG4gIH0sIFtlcnJvcl0pO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPG1haW4gY2xhc3NOYW1lPVwicHktMzIgbWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWIgZnJvbS1yaWNlIHZpYS1zYW5kLWxpZ2h0LzUwIHRvLW9jZWFuLzEwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctMnhsIG14LWF1dG8gcHgtNCB0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTV4bCBtZDp0ZXh0LTZ4bCBmb250LWRpc3BsYXkgdGV4dC10ZW1wbGUgdHJhY2tpbmctdGlnaHQgbWItNlwiPlxyXG4gICAgICAgICAgVXBzIVxyXG4gICAgICAgIDwvaDE+XHJcbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIG1kOnRleHQtM3hsIGZvbnQtZGlzcGxheSB0ZXh0LXRlbXBsZSBtYi00XCI+XHJcbiAgICAgICAgICBDb8WbIHBvc3rFgm8gbmllIHRha1xyXG4gICAgICAgIDwvaDI+XHJcbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LXdvb2QtbGlnaHQvODAgbWItOFwiPlxyXG4gICAgICAgICAgUHJ6ZXByYXN6YW15LCB3eXN0xIVwacWCIG5pZW9jemVraXdhbnkgYsWCxIVkLiBOYXN6IHplc3DDs8WCIHpvc3RhxYIgcG93aWFkb21pb255LlxyXG4gICAgICAgIDwvcD5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgZ2FwLTQganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcmVzZXQoKX1cclxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB4LTYgcHktMyBiZy10ZW1wbGUgdGV4dC1yaWNlIHJvdW5kZWQtZnVsbCBob3ZlcjpiZy10ZW1wbGUvOTAgdHJhbnNpdGlvbi1jb2xvcnNcIlxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICBTcHLDs2J1aiBwb25vd25pZVxyXG4gICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICBocmVmPVwiL1wiXHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweC02IHB5LTMgYmctcmljZSB0ZXh0LXRlbXBsZSBib3JkZXIgYm9yZGVyLXRlbXBsZS8yMCByb3VuZGVkLWZ1bGwgaG92ZXI6YmctcmljZS84MCB0cmFuc2l0aW9uLWNvbG9yc1wiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIFdyw7PEhyBuYSBzdHJvbsSZIGfFgsOzd27EhVxyXG4gICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvbWFpbj5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbInVzZUVmZmVjdCIsIkxpbmsiLCJFcnJvciIsImVycm9yIiwicmVzZXQiLCJjb25zb2xlIiwid2FybiIsIm1lc3NhZ2UiLCJzdGFjayIsImxvZ2dpbmdFcnJvciIsIm1haW4iLCJjbGFzc05hbWUiLCJkaXYiLCJoMSIsImgyIiwicCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJocmVmIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AsyncCSS.jsx":
/*!*************************************!*\
  !*** ./src/components/AsyncCSS.jsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AsyncCSS)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction AsyncCSS() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"AsyncCSS.useEffect\": ()=>{\n            // Since globals.css is already loaded by Next.js, we don't need to load it again\n            // This component can be used for other non-critical CSS in the future\n            // Add any additional non-critical CSS loading here if needed\n            console.log('AsyncCSS: Ready for non-critical CSS loading');\n        }\n    }[\"AsyncCSS.useEffect\"], []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Bc3luY0NTUy5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7OzZEQUVrQztBQUVuQixTQUFTQztJQUN0QkQsZ0RBQVNBOzhCQUFDO1lBQ1IsaUZBQWlGO1lBQ2pGLHNFQUFzRTtZQUV0RSw2REFBNkQ7WUFDN0RFLFFBQVFDLEdBQUcsQ0FBQztRQUNkOzZCQUFHLEVBQUU7SUFFTCxPQUFPO0FBQ1QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGF2aWRcXERlc2t0b3BcXFByb2pla3R5XFxibG9nX3RlbXBsYXRlXFxteS10cmF2ZWwtYmxvZ1xcc3JjXFxjb21wb25lbnRzXFxBc3luY0NTUy5qc3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFzeW5jQ1NTKCkge1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFNpbmNlIGdsb2JhbHMuY3NzIGlzIGFscmVhZHkgbG9hZGVkIGJ5IE5leHQuanMsIHdlIGRvbid0IG5lZWQgdG8gbG9hZCBpdCBhZ2FpblxuICAgIC8vIFRoaXMgY29tcG9uZW50IGNhbiBiZSB1c2VkIGZvciBvdGhlciBub24tY3JpdGljYWwgQ1NTIGluIHRoZSBmdXR1cmVcblxuICAgIC8vIEFkZCBhbnkgYWRkaXRpb25hbCBub24tY3JpdGljYWwgQ1NTIGxvYWRpbmcgaGVyZSBpZiBuZWVkZWRcbiAgICBjb25zb2xlLmxvZygnQXN5bmNDU1M6IFJlYWR5IGZvciBub24tY3JpdGljYWwgQ1NTIGxvYWRpbmcnKTtcbiAgfSwgW10pO1xuXG4gIHJldHVybiBudWxsO1xufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsIkFzeW5jQ1NTIiwiY29uc29sZSIsImxvZyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AsyncCSS.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ClientAnalytics.jsx":
/*!********************************************!*\
  !*** ./src/components/ClientAnalytics.jsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientAnalytics: () => (/* binding */ ClientAnalytics)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ ClientAnalytics auto */ \nfunction ClientAnalytics() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"ClientAnalytics.useEffect\": ()=>{\n            // Bezpieczne ładowanie analytics tylko w przeglądarce\n            if (true) return;\n            // Google Analytics\n            const GA_ID = \"G-M780DCS04D\";\n            if (GA_ID && !window.gtag) {\n                // Załaduj gtag script\n                const script = document.createElement('script');\n                script.async = true;\n                script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_ID}`;\n                document.head.appendChild(script);\n                // Inicjalizuj gtag\n                window.dataLayer = window.dataLayer || [];\n                function gtag() {\n                    window.dataLayer.push(arguments);\n                }\n                window.gtag = gtag;\n                gtag('js', new Date());\n                gtag('config', GA_ID);\n            }\n            // Web Vitals tracking (tylko w produkcji)\n            if (false) {}\n        }\n    }[\"ClientAnalytics.useEffect\"], []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ClientAnalytics.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ClientResourcePreloader.jsx":
/*!****************************************************!*\
  !*** ./src/components/ClientResourcePreloader.jsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientResourcePreloader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n// Lazy load ResourcePreloader only on client side\nconst ResourcePreloader = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\ClientResourcePreloader.jsx -> \" + \"./ResourcePreloader\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\nfunction ClientResourcePreloader() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResourcePreloader, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\ClientResourcePreloader.jsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DbGllbnRSZXNvdXJjZVByZWxvYWRlci5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFbUM7QUFFbkMsa0RBQWtEO0FBQ2xELE1BQU1DLG9CQUFvQkQsd0RBQU9BOzs7Ozs7OztJQUMvQkUsS0FBSztJQUNMQyxTQUFTLElBQU07O0FBR0YsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNIOzs7OztBQUNWIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRhdmlkXFxEZXNrdG9wXFxQcm9qZWt0eVxcYmxvZ190ZW1wbGF0ZVxcbXktdHJhdmVsLWJsb2dcXHNyY1xcY29tcG9uZW50c1xcQ2xpZW50UmVzb3VyY2VQcmVsb2FkZXIuanN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IGR5bmFtaWMgZnJvbSAnbmV4dC9keW5hbWljJztcblxuLy8gTGF6eSBsb2FkIFJlc291cmNlUHJlbG9hZGVyIG9ubHkgb24gY2xpZW50IHNpZGVcbmNvbnN0IFJlc291cmNlUHJlbG9hZGVyID0gZHluYW1pYygoKSA9PiBpbXBvcnQoJy4vUmVzb3VyY2VQcmVsb2FkZXInKSwge1xuICBzc3I6IGZhbHNlLFxuICBsb2FkaW5nOiAoKSA9PiBudWxsLFxufSk7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENsaWVudFJlc291cmNlUHJlbG9hZGVyKCkge1xuICByZXR1cm4gPFJlc291cmNlUHJlbG9hZGVyIC8+O1xufVxuIl0sIm5hbWVzIjpbImR5bmFtaWMiLCJSZXNvdXJjZVByZWxvYWRlciIsInNzciIsImxvYWRpbmciLCJDbGllbnRSZXNvdXJjZVByZWxvYWRlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ClientResourcePreloader.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CookieConsent.jsx":
/*!******************************************!*\
  !*** ./src/components/CookieConsent.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CookieConsentBanner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction CookieConsentBanner() {\n    const [showBanner, setShowBanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CookieConsentBanner.useEffect\": ()=>{\n            // Sprawdź czy użytkownik już wyraził zgodę\n            const consent = localStorage.getItem('bakasana-cookie-consent');\n            if (!consent) {\n                setShowBanner(true);\n                // Pokaż banner z małym opóźnieniem dla lepszego UX\n                setTimeout({\n                    \"CookieConsentBanner.useEffect\": ()=>setIsVisible(true)\n                }[\"CookieConsentBanner.useEffect\"], 1000);\n            }\n        }\n    }[\"CookieConsentBanner.useEffect\"], []);\n    const handleAccept = ()=>{\n        localStorage.setItem('bakasana-cookie-consent', 'accepted');\n        setIsVisible(false);\n        setTimeout(()=>setShowBanner(false), 300);\n        // Włącz Google Analytics\n        if (false) {}\n    };\n    const handleDecline = ()=>{\n        localStorage.setItem('bakasana-cookie-consent', 'declined');\n        setIsVisible(false);\n        setTimeout(()=>setShowBanner(false), 300);\n        // Wyłącz Google Analytics\n        if (false) {}\n    };\n    if (!showBanner) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `fixed bottom-0 left-0 right-0 z-50 transition-transform duration-300 ${isVisible ? 'translate-y-0' : 'translate-y-full'}`,\n        style: {\n            background: \"rgba(139, 115, 85, 0.95)\",\n            backdropFilter: \"blur(10px)\",\n            borderTop: \"1px solid rgba(139, 115, 85, 0.2)\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 py-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row items-start sm:items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-2 text-sm\",\n                                children: \"\\uD83C\\uDF6A Ta strona używa plik\\xf3w cookies, aby zapewnić najlepsze doświadczenia użytkownika.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\CookieConsent.jsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs opacity-90\",\n                                children: [\n                                    \"Używamy cookies do analizy ruchu i personalizacji treści.\",\n                                    ' ',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/polityka-prywatnosci\",\n                                        className: \"underline hover:no-underline\",\n                                        children: \"Dowiedz się więcej\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\CookieConsent.jsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\CookieConsent.jsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\CookieConsent.jsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDecline,\n                                className: \"px-4 py-2 text-sm text-white border border-white/30 rounded-full hover:bg-white/10 transition-colors\",\n                                children: \"Odrzuć\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\CookieConsent.jsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleAccept,\n                                className: \"px-6 py-2 text-sm bg-temple text-white rounded-full hover:bg-temple/90 transition-colors shadow-lg\",\n                                children: \"Akceptuję\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\CookieConsent.jsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\CookieConsent.jsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\CookieConsent.jsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\CookieConsent.jsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\CookieConsent.jsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CookieConsent.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PWAInstaller.jsx":
/*!*****************************************!*\
  !*** ./src/components/PWAInstaller.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PWAFeatures: () => (/* binding */ PWAFeatures),\n/* harmony export */   \"default\": () => (/* binding */ PWAInstaller),\n/* harmony export */   usePWAInstall: () => (/* binding */ usePWAInstall)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default,usePWAInstall,PWAFeatures auto */ \n\n\nfunction PWAInstaller() {\n    const [deferredPrompt, setDeferredPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInstallPrompt, setShowInstallPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isInstalled, setIsInstalled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isIOS, setIsIOS] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStandalone, setIsStandalone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PWAInstaller.useEffect\": ()=>{\n            // Check if app is already installed\n            const checkIfInstalled = {\n                \"PWAInstaller.useEffect.checkIfInstalled\": ()=>{\n                    const isStandaloneMode = window.matchMedia('(display-mode: standalone)').matches;\n                    const isIOSStandalone = window.navigator.standalone === true;\n                    setIsStandalone(isStandaloneMode || isIOSStandalone);\n                    setIsInstalled(isStandaloneMode || isIOSStandalone);\n                }\n            }[\"PWAInstaller.useEffect.checkIfInstalled\"];\n            // Check if iOS\n            const checkIfIOS = {\n                \"PWAInstaller.useEffect.checkIfIOS\": ()=>{\n                    const isIOSDevice = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;\n                    setIsIOS(isIOSDevice);\n                }\n            }[\"PWAInstaller.useEffect.checkIfIOS\"];\n            // Register service worker\n            const registerServiceWorker = {\n                \"PWAInstaller.useEffect.registerServiceWorker\": async ()=>{\n                    if ('serviceWorker' in navigator) {\n                        try {\n                            const registration = await navigator.serviceWorker.register('/sw.js');\n                            console.log('Service Worker registered:', registration);\n                            // Check for updates\n                            registration.addEventListener('updatefound', {\n                                \"PWAInstaller.useEffect.registerServiceWorker\": ()=>{\n                                    const newWorker = registration.installing;\n                                    newWorker.addEventListener('statechange', {\n                                        \"PWAInstaller.useEffect.registerServiceWorker\": ()=>{\n                                            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {\n                                                // New content available, show update prompt\n                                                showUpdatePrompt();\n                                            }\n                                        }\n                                    }[\"PWAInstaller.useEffect.registerServiceWorker\"]);\n                                }\n                            }[\"PWAInstaller.useEffect.registerServiceWorker\"]);\n                        } catch (error) {\n                            console.error('Service Worker registration failed:', error);\n                        }\n                    }\n                }\n            }[\"PWAInstaller.useEffect.registerServiceWorker\"];\n            // Listen for beforeinstallprompt event\n            const handleBeforeInstallPrompt = {\n                \"PWAInstaller.useEffect.handleBeforeInstallPrompt\": (e)=>{\n                    e.preventDefault();\n                    setDeferredPrompt(e);\n                    // Show install prompt after user has been on site for a while\n                    setTimeout({\n                        \"PWAInstaller.useEffect.handleBeforeInstallPrompt\": ()=>{\n                            if (!isInstalled && !localStorage.getItem('pwa-install-dismissed')) {\n                                setShowInstallPrompt(true);\n                            }\n                        }\n                    }[\"PWAInstaller.useEffect.handleBeforeInstallPrompt\"], 30000); // Show after 30 seconds\n                }\n            }[\"PWAInstaller.useEffect.handleBeforeInstallPrompt\"];\n            checkIfInstalled();\n            checkIfIOS();\n            registerServiceWorker();\n            window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n            return ({\n                \"PWAInstaller.useEffect\": ()=>{\n                    window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n                }\n            })[\"PWAInstaller.useEffect\"];\n        }\n    }[\"PWAInstaller.useEffect\"], [\n        isInstalled\n    ]);\n    const handleInstallClick = async ()=>{\n        if (!deferredPrompt) return;\n        deferredPrompt.prompt();\n        const { outcome } = await deferredPrompt.userChoice;\n        if (outcome === 'accepted') {\n            console.log('User accepted the install prompt');\n            setIsInstalled(true);\n        } else {\n            console.log('User dismissed the install prompt');\n            localStorage.setItem('pwa-install-dismissed', 'true');\n        }\n        setDeferredPrompt(null);\n        setShowInstallPrompt(false);\n    };\n    const handleDismiss = ()=>{\n        setShowInstallPrompt(false);\n        localStorage.setItem('pwa-install-dismissed', 'true');\n    };\n    const showUpdatePrompt = ()=>{\n        if (confirm('Nowa wersja aplikacji jest dostępna. Czy chcesz ją załadować?')) {\n            window.location.reload();\n        }\n    };\n    // Don't show if already installed or on iOS (different install process)\n    if (isInstalled || isStandalone) {\n        return null;\n    }\n    // iOS install instructions\n    if (isIOS && showInstallPrompt) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 100\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                exit: {\n                    opacity: 0,\n                    y: 100\n                },\n                className: \"fixed bottom-4 left-4 right-4 z-50 max-w-sm mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-2xl p-6 border border-temple/10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl\",\n                                children: \"\\uD83D\\uDCF1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                lineNumber: 121,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-temple mb-2\",\n                                        children: \"Zainstaluj aplikację\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-wood-light mb-4\",\n                                        children: \"Dodaj Bali Yoga Journey do ekranu gł\\xf3wnego:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-xs text-wood-light\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"1.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: 'Naciśnij przycisk \"Udostępnij\" ⬆️'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"2.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: 'Wybierz \"Dodaj do ekranu gł\\xf3wnego\" ➕'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                lineNumber: 122,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDismiss,\n                                className: \"text-wood-light hover:text-temple transition-colors\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                        lineNumber: 120,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                    lineNumber: 119,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n            lineNumber: 112,\n            columnNumber: 7\n        }, this);\n    }\n    // Android/Desktop install prompt\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: showInstallPrompt && deferredPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 100\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            exit: {\n                opacity: 0,\n                y: 100\n            },\n            className: \"fixed bottom-4 left-4 right-4 z-50 max-w-sm mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-temple to-golden rounded-2xl shadow-2xl p-6 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-2xl\",\n                            children: \"\\uD83E\\uDDD8‍♀️\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                            lineNumber: 165,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium mb-2\",\n                                    children: \"Zainstaluj aplikację Bali Yoga Journey\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm opacity-90 mb-4\",\n                                    children: \"Szybki dostęp do retreat\\xf3w, map i rezerwacji. Działa offline!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleInstallClick,\n                                            className: \"bg-white text-temple px-4 py-2 rounded-lg text-sm font-medium hover:bg-white/90 transition-colors\",\n                                            children: \"Zainstaluj\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleDismiss,\n                                            className: \"text-white/80 hover:text-white transition-colors text-sm\",\n                                            children: \"Nie teraz\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                            lineNumber: 166,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                    lineNumber: 164,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                lineNumber: 163,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n            lineNumber: 157,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n// Hook for checking PWA install status\nfunction usePWAInstall() {\n    const [isInstalled, setIsInstalled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [canInstall, setCanInstall] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"usePWAInstall.useEffect\": ()=>{\n            const checkInstallStatus = {\n                \"usePWAInstall.useEffect.checkInstallStatus\": ()=>{\n                    const isStandaloneMode = window.matchMedia('(display-mode: standalone)').matches;\n                    const isIOSStandalone = window.navigator.standalone === true;\n                    setIsInstalled(isStandaloneMode || isIOSStandalone);\n                }\n            }[\"usePWAInstall.useEffect.checkInstallStatus\"];\n            const handleBeforeInstallPrompt = {\n                \"usePWAInstall.useEffect.handleBeforeInstallPrompt\": (e)=>{\n                    e.preventDefault();\n                    setCanInstall(true);\n                }\n            }[\"usePWAInstall.useEffect.handleBeforeInstallPrompt\"];\n            checkInstallStatus();\n            window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n            return ({\n                \"usePWAInstall.useEffect\": ()=>{\n                    window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n                }\n            })[\"usePWAInstall.useEffect\"];\n        }\n    }[\"usePWAInstall.useEffect\"], []);\n    return {\n        isInstalled,\n        canInstall\n    };\n}\n// Component for showing PWA features\nfunction PWAFeatures() {\n    const { isInstalled } = usePWAInstall();\n    if (!isInstalled) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-temple/5 rounded-xl p-6 mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-serif text-temple mb-4\",\n                children: \"\\uD83C\\uDF89 Aplikacja zainstalowana!\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-4 text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-temple\",\n                                children: \"\\uD83D\\uDCF1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Szybki dostęp\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-temple\",\n                                children: \"⚡\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Działa offline\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-temple\",\n                                children: \"\\uD83D\\uDD14\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Powiadomienia\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-temple\",\n                                children: \"\\uD83D\\uDCBE\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Mniej danych\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_template\\\\my-travel-blog\\\\src\\\\components\\\\PWAInstaller.jsx\",\n        lineNumber: 231,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PWAInstaller.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SmoothScroll.jsx":
/*!*****************************************!*\
  !*** ./src/components/SmoothScroll.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SmoothScroll)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction SmoothScroll() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"SmoothScroll.useEffect\": ()=>{\n            // Smooth scroll z offset dla navbar\n            const handleSmoothScroll = {\n                \"SmoothScroll.useEffect.handleSmoothScroll\": (e)=>{\n                    const href = e.currentTarget.getAttribute('href');\n                    if (href && href.startsWith('#')) {\n                        e.preventDefault();\n                        const target = document.querySelector(href);\n                        const navbar = document.querySelector('.navbar, header');\n                        if (target) {\n                            const navHeight = navbar ? navbar.offsetHeight : 0;\n                            const targetPosition = target.offsetTop - navHeight - 20;\n                            window.scrollTo({\n                                top: targetPosition,\n                                behavior: 'smooth'\n                            });\n                        }\n                    }\n                }\n            }[\"SmoothScroll.useEffect.handleSmoothScroll\"];\n            // Dodaj scroll behavior do wszystkich anchor linków\n            const anchorLinks = document.querySelectorAll('a[href^=\"#\"]');\n            anchorLinks.forEach({\n                \"SmoothScroll.useEffect\": (anchor)=>{\n                    anchor.addEventListener('click', handleSmoothScroll);\n                }\n            }[\"SmoothScroll.useEffect\"]);\n            // Navbar scroll effect\n            const handleNavbarScroll = {\n                \"SmoothScroll.useEffect.handleNavbarScroll\": ()=>{\n                    const navbar = document.querySelector('.navbar, header');\n                    if (navbar) {\n                        if (window.scrollY > 20) {\n                            navbar.classList.add('scrolled');\n                        } else {\n                            navbar.classList.remove('scrolled');\n                        }\n                    }\n                }\n            }[\"SmoothScroll.useEffect.handleNavbarScroll\"];\n            window.addEventListener('scroll', handleNavbarScroll, {\n                passive: true\n            });\n            // Cleanup\n            return ({\n                \"SmoothScroll.useEffect\": ()=>{\n                    anchorLinks.forEach({\n                        \"SmoothScroll.useEffect\": (anchor)=>{\n                            anchor.removeEventListener('click', handleSmoothScroll);\n                        }\n                    }[\"SmoothScroll.useEffect\"]);\n                    window.removeEventListener('scroll', handleNavbarScroll);\n                }\n            })[\"SmoothScroll.useEffect\"];\n        }\n    }[\"SmoothScroll.useEffect\"], []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SmoothScroll.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/WebVitalsMonitor.jsx":
/*!*********************************************!*\
  !*** ./src/components/WebVitalsMonitor.jsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WebVitalsMonitor)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction WebVitalsMonitor() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"WebVitalsMonitor.useEffect\": ()=>{\n            // Only run in production or when explicitly enabled\n            if ( true && !process.env.NEXT_PUBLIC_MONITOR_VITALS) {\n                return;\n            }\n            // Web Vitals monitoring\n            const vitalsData = {\n                lcp: null,\n                fid: null,\n                cls: null,\n                fcp: null,\n                ttfb: null\n            };\n            // Function to send vitals to analytics\n            const sendToAnalytics = {\n                \"WebVitalsMonitor.useEffect.sendToAnalytics\": (metric)=>{\n                    // Send to Google Analytics 4\n                    if (false) {}\n                    // Send to Vercel Analytics\n                    if (false) {}\n                    // Log to console in development\n                    if (true) {\n                        console.log(`${metric.name}:`, metric.value, metric);\n                    }\n                    // Store for potential API sending\n                    vitalsData[metric.name.toLowerCase()] = metric.value;\n                }\n            }[\"WebVitalsMonitor.useEffect.sendToAnalytics\"];\n            // Largest Contentful Paint (LCP)\n            const observeLCP = {\n                \"WebVitalsMonitor.useEffect.observeLCP\": ()=>{\n                    if ('PerformanceObserver' in window) {\n                        try {\n                            const observer = new PerformanceObserver({\n                                \"WebVitalsMonitor.useEffect.observeLCP\": (list)=>{\n                                    const entries = list.getEntries();\n                                    const lastEntry = entries[entries.length - 1];\n                                    sendToAnalytics({\n                                        name: 'LCP',\n                                        value: lastEntry.startTime,\n                                        id: generateUniqueId(),\n                                        entries: entries\n                                    });\n                                }\n                            }[\"WebVitalsMonitor.useEffect.observeLCP\"]);\n                            observer.observe({\n                                entryTypes: [\n                                    'largest-contentful-paint'\n                                ]\n                            });\n                        } catch (e) {\n                            console.warn('LCP observation failed:', e);\n                        }\n                    }\n                }\n            }[\"WebVitalsMonitor.useEffect.observeLCP\"];\n            // First Input Delay (FID)\n            const observeFID = {\n                \"WebVitalsMonitor.useEffect.observeFID\": ()=>{\n                    if ('PerformanceObserver' in window) {\n                        try {\n                            const observer = new PerformanceObserver({\n                                \"WebVitalsMonitor.useEffect.observeFID\": (list)=>{\n                                    const entries = list.getEntries();\n                                    entries.forEach({\n                                        \"WebVitalsMonitor.useEffect.observeFID\": (entry)=>{\n                                            sendToAnalytics({\n                                                name: 'FID',\n                                                value: entry.processingStart - entry.startTime,\n                                                id: generateUniqueId(),\n                                                entries: [\n                                                    entry\n                                                ]\n                                            });\n                                        }\n                                    }[\"WebVitalsMonitor.useEffect.observeFID\"]);\n                                }\n                            }[\"WebVitalsMonitor.useEffect.observeFID\"]);\n                            observer.observe({\n                                entryTypes: [\n                                    'first-input'\n                                ]\n                            });\n                        } catch (e) {\n                            console.warn('FID observation failed:', e);\n                        }\n                    }\n                }\n            }[\"WebVitalsMonitor.useEffect.observeFID\"];\n            // Cumulative Layout Shift (CLS)\n            const observeCLS = {\n                \"WebVitalsMonitor.useEffect.observeCLS\": ()=>{\n                    if ('PerformanceObserver' in window) {\n                        try {\n                            let clsValue = 0;\n                            let sessionValue = 0;\n                            let sessionEntries = [];\n                            const observer = new PerformanceObserver({\n                                \"WebVitalsMonitor.useEffect.observeCLS\": (list)=>{\n                                    const entries = list.getEntries();\n                                    entries.forEach({\n                                        \"WebVitalsMonitor.useEffect.observeCLS\": (entry)=>{\n                                            // Only count layout shifts without recent input\n                                            if (!entry.hadRecentInput) {\n                                                const firstSessionEntry = sessionEntries[0];\n                                                const lastSessionEntry = sessionEntries[sessionEntries.length - 1];\n                                                // If the entry occurred less than 1 second after the previous entry\n                                                // and less than 5 seconds after the first entry in the session,\n                                                // include the entry in the current session. Otherwise, start a new session.\n                                                if (sessionValue && entry.startTime - lastSessionEntry.startTime < 1000 && entry.startTime - firstSessionEntry.startTime < 5000) {\n                                                    sessionValue += entry.value;\n                                                    sessionEntries.push(entry);\n                                                } else {\n                                                    sessionValue = entry.value;\n                                                    sessionEntries = [\n                                                        entry\n                                                    ];\n                                                }\n                                                // If the current session value is larger than the current CLS value,\n                                                // update CLS and the entries contributing to it.\n                                                if (sessionValue > clsValue) {\n                                                    clsValue = sessionValue;\n                                                    sendToAnalytics({\n                                                        name: 'CLS',\n                                                        value: clsValue,\n                                                        id: generateUniqueId(),\n                                                        entries: sessionEntries\n                                                    });\n                                                }\n                                            }\n                                        }\n                                    }[\"WebVitalsMonitor.useEffect.observeCLS\"]);\n                                }\n                            }[\"WebVitalsMonitor.useEffect.observeCLS\"]);\n                            observer.observe({\n                                entryTypes: [\n                                    'layout-shift'\n                                ]\n                            });\n                        } catch (e) {\n                            console.warn('CLS observation failed:', e);\n                        }\n                    }\n                }\n            }[\"WebVitalsMonitor.useEffect.observeCLS\"];\n            // First Contentful Paint (FCP)\n            const observeFCP = {\n                \"WebVitalsMonitor.useEffect.observeFCP\": ()=>{\n                    if ('PerformanceObserver' in window) {\n                        try {\n                            const observer = new PerformanceObserver({\n                                \"WebVitalsMonitor.useEffect.observeFCP\": (list)=>{\n                                    const entries = list.getEntries();\n                                    entries.forEach({\n                                        \"WebVitalsMonitor.useEffect.observeFCP\": (entry)=>{\n                                            if (entry.name === 'first-contentful-paint') {\n                                                sendToAnalytics({\n                                                    name: 'FCP',\n                                                    value: entry.startTime,\n                                                    id: generateUniqueId(),\n                                                    entries: [\n                                                        entry\n                                                    ]\n                                                });\n                                            }\n                                        }\n                                    }[\"WebVitalsMonitor.useEffect.observeFCP\"]);\n                                }\n                            }[\"WebVitalsMonitor.useEffect.observeFCP\"]);\n                            observer.observe({\n                                entryTypes: [\n                                    'paint'\n                                ]\n                            });\n                        } catch (e) {\n                            console.warn('FCP observation failed:', e);\n                        }\n                    }\n                }\n            }[\"WebVitalsMonitor.useEffect.observeFCP\"];\n            // Time to First Byte (TTFB)\n            const observeTTFB = {\n                \"WebVitalsMonitor.useEffect.observeTTFB\": ()=>{\n                    if ('PerformanceObserver' in window) {\n                        try {\n                            const observer = new PerformanceObserver({\n                                \"WebVitalsMonitor.useEffect.observeTTFB\": (list)=>{\n                                    const entries = list.getEntries();\n                                    entries.forEach({\n                                        \"WebVitalsMonitor.useEffect.observeTTFB\": (entry)=>{\n                                            if (entry.entryType === 'navigation') {\n                                                sendToAnalytics({\n                                                    name: 'TTFB',\n                                                    value: entry.responseStart - entry.requestStart,\n                                                    id: generateUniqueId(),\n                                                    entries: [\n                                                        entry\n                                                    ]\n                                                });\n                                            }\n                                        }\n                                    }[\"WebVitalsMonitor.useEffect.observeTTFB\"]);\n                                }\n                            }[\"WebVitalsMonitor.useEffect.observeTTFB\"]);\n                            observer.observe({\n                                entryTypes: [\n                                    'navigation'\n                                ]\n                            });\n                        } catch (e) {\n                            console.warn('TTFB observation failed:', e);\n                        }\n                    }\n                }\n            }[\"WebVitalsMonitor.useEffect.observeTTFB\"];\n            // Generate unique ID for each metric\n            const generateUniqueId = {\n                \"WebVitalsMonitor.useEffect.generateUniqueId\": ()=>{\n                    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n                }\n            }[\"WebVitalsMonitor.useEffect.generateUniqueId\"];\n            // Start observing all metrics\n            observeLCP();\n            observeFID();\n            observeCLS();\n            observeFCP();\n            observeTTFB();\n            // Additional performance monitoring\n            const monitorResourceTiming = {\n                \"WebVitalsMonitor.useEffect.monitorResourceTiming\": ()=>{\n                    if ('PerformanceObserver' in window) {\n                        try {\n                            const observer = new PerformanceObserver({\n                                \"WebVitalsMonitor.useEffect.monitorResourceTiming\": (list)=>{\n                                    const entries = list.getEntries();\n                                    entries.forEach({\n                                        \"WebVitalsMonitor.useEffect.monitorResourceTiming\": (entry)=>{\n                                            // Monitor slow resources (>1s)\n                                            if (entry.duration > 1000) {\n                                                console.warn('Slow resource detected:', {\n                                                    name: entry.name,\n                                                    duration: entry.duration,\n                                                    size: entry.transferSize\n                                                });\n                                                // Send to analytics\n                                                if (false) {}\n                                            }\n                                        }\n                                    }[\"WebVitalsMonitor.useEffect.monitorResourceTiming\"]);\n                                }\n                            }[\"WebVitalsMonitor.useEffect.monitorResourceTiming\"]);\n                            observer.observe({\n                                entryTypes: [\n                                    'resource'\n                                ]\n                            });\n                        } catch (e) {\n                            console.warn('Resource timing observation failed:', e);\n                        }\n                    }\n                }\n            }[\"WebVitalsMonitor.useEffect.monitorResourceTiming\"];\n            // Monitor long tasks (>50ms)\n            const monitorLongTasks = {\n                \"WebVitalsMonitor.useEffect.monitorLongTasks\": ()=>{\n                    if ('PerformanceObserver' in window) {\n                        try {\n                            const observer = new PerformanceObserver({\n                                \"WebVitalsMonitor.useEffect.monitorLongTasks\": (list)=>{\n                                    const entries = list.getEntries();\n                                    entries.forEach({\n                                        \"WebVitalsMonitor.useEffect.monitorLongTasks\": (entry)=>{\n                                            console.warn('Long task detected:', {\n                                                duration: entry.duration,\n                                                startTime: entry.startTime\n                                            });\n                                            // Send to analytics\n                                            if (false) {}\n                                        }\n                                    }[\"WebVitalsMonitor.useEffect.monitorLongTasks\"]);\n                                }\n                            }[\"WebVitalsMonitor.useEffect.monitorLongTasks\"]);\n                            observer.observe({\n                                entryTypes: [\n                                    'longtask'\n                                ]\n                            });\n                        } catch (e) {\n                            console.warn('Long task observation failed:', e);\n                        }\n                    }\n                }\n            }[\"WebVitalsMonitor.useEffect.monitorLongTasks\"];\n            // Start additional monitoring\n            monitorResourceTiming();\n            monitorLongTasks();\n            // Send aggregated vitals data to API endpoint (optional)\n            const sendVitalsToAPI = {\n                \"WebVitalsMonitor.useEffect.sendVitalsToAPI\": ()=>{\n                    if (Object.values(vitalsData).some({\n                        \"WebVitalsMonitor.useEffect.sendVitalsToAPI\": (value)=>value !== null\n                    }[\"WebVitalsMonitor.useEffect.sendVitalsToAPI\"])) {\n                        fetch('/api/vitals', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                ...vitalsData,\n                                url: window.location.pathname,\n                                userAgent: navigator.userAgent,\n                                timestamp: Date.now()\n                            })\n                        }).catch({\n                            \"WebVitalsMonitor.useEffect.sendVitalsToAPI\": (err)=>{\n                                console.warn('Failed to send vitals to API:', err);\n                            }\n                        }[\"WebVitalsMonitor.useEffect.sendVitalsToAPI\"]);\n                    }\n                }\n            }[\"WebVitalsMonitor.useEffect.sendVitalsToAPI\"];\n            // Send vitals data when page is about to unload\n            window.addEventListener('beforeunload', sendVitalsToAPI);\n            // Cleanup\n            return ({\n                \"WebVitalsMonitor.useEffect\": ()=>{\n                    window.removeEventListener('beforeunload', sendVitalsToAPI);\n                }\n            })[\"WebVitalsMonitor.useEffect\"];\n        }\n    }[\"WebVitalsMonitor.useEffect\"], []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/WebVitalsMonitor.jsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/@vercel"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.jsx&appDir=C%3A%5CUsers%5Cdavid%5CDesktop%5CProjekty%5Cblog_template%5Cmy-travel-blog%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdavid%5CDesktop%5CProjekty%5Cblog_template%5Cmy-travel-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();