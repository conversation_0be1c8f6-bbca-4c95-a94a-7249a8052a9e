# 🌐 KONFIGURACJA WŁASNEJ DOMENY: bakasana-travel.blog

## 🎯 **PRZEGLĄD**

<PERSON><PERSON> domena `bakasana-travel.blog` jest już skonfigurowana w kodzie. Teraz musimy:
1. Zdeployować stronę na Vercel
2. Podłączyć domenę w Vercel
3. Skonfigurować DNS u dostawcy domeny

## 🚀 **KROK 1: DEPLOY NA VERCEL**

1. **Idź na [vercel.com](https://vercel.com)**
2. **Zaloguj się przez GitHub**
3. **"New Project" → wybierz `my-travel-blog`**
4. **<PERSON><PERSON>nij "Deploy"**
5. **Czekaj 2-3 minuty na build**

## 🔧 **KROK 2: DODAJ DOMENĘ W VERCEL**

Po udanym deploymencie:

1. **Idź do projektu w Vercel dashboard**
2. **<PERSON><PERSON><PERSON>j "Settings" → "Domains"**
3. **<PERSON><PERSON><PERSON>j "Add Domain"**
4. **Wpisz `bakasana-travel.blog`**
5. **<PERSON><PERSON><PERSON>j "Add"**
6. **Vercel pokaże instrukcje DNS** - **ZAPISZ TE INFORMACJE!**

Vercel pokaże jedną z dwóch opcji:

### **Opcja A: Konfiguracja przez CNAME (zalecane)**
```
Type: CNAME
Name: @
Value: cname.vercel-dns.com
```

### **Opcja B: Konfiguracja przez A Records**
```
Type: A
Name: @
Value: 76.76.21.21
```

## 🌍 **KROK 3: KONFIGURACJA DNS U DOSTAWCY DOMENY**

Teraz musisz skonfigurować DNS u dostawcy, gdzie kupiłaś domenę `bakasana-travel.blog`.

### **Znajdź panel DNS u swojego dostawcy**

1. **Zaloguj się do panelu dostawcy domeny**
2. **Znajdź sekcję "DNS", "DNS Management" lub "Nameservers"**

### **Dodaj rekordy DNS**

Dodaj rekordy, które pokazał Vercel (CNAME lub A record).

#### **Dla większości dostawców:**

1. **Znajdź "DNS Records" lub "Zone Editor"**
2. **Kliknij "Add Record" lub podobny przycisk**
3. **Wybierz typ rekordu (CNAME lub A)**
4. **Dodaj wartości z Vercel**
5. **Zapisz zmiany**

#### **Dodatkowo dodaj rekordy dla www:**
```
Type: CNAME
Name: www
Value: cname.vercel-dns.com
```

### **Popularne panele dostawców domen:**

#### **Namecheap:**
- Advanced DNS → Add New Record

#### **GoDaddy:**
- DNS Management → Add Record

#### **OVH:**
- DNS Zone → Add an entry

#### **Google Domains:**
- DNS → Custom resource records

## ⏱️ **KROK 4: CZEKAJ NA PROPAGACJĘ DNS**

- DNS może potrzebować od 15 minut do 48 godzin na pełną propagację
- Vercel będzie automatycznie sprawdzać czy DNS jest poprawnie skonfigurowany
- Status możesz sprawdzić w Vercel dashboard (Domains section)

## 🔒 **KROK 5: HTTPS I SSL**

Vercel automatycznie skonfiguruje HTTPS dla Twojej domeny. Nie musisz nic robić!

## 🔍 **KROK 6: GOOGLE SEARCH CONSOLE Z WŁASNĄ DOMENĄ**

1. **Idź na [search.google.com/search-console](https://search.google.com/search-console)**
2. **Dodaj swoją domenę** (wybierz opcję "Domain property")
3. **Wpisz `bakasana-travel.blog`**
4. **Zweryfikuj przez DNS** (dodaj rekord TXT, który Google pokaże)
5. **Prześlij sitemap:** `https://bakasana-travel.blog/sitemap.xml`

## 📊 **KROK 7: GOOGLE ANALYTICS Z WŁASNĄ DOMENĄ**

1. **Idź do Google Analytics**
2. **Admin → Property Settings**
3. **Upewnij się, że domena jest poprawna**
4. **Sprawdź czy tracking działa** (Real-time)

## ✅ **CHECKLIST PO KONFIGURACJI**

- [ ] **Strona ładuje się pod adresem `https://bakasana-travel.blog`**
- [ ] **HTTPS działa (zielona kłódka w przeglądarce)**
- [ ] **Przekierowanie z `www` działa**
- [ ] **Wszystkie linki wewnętrzne używają domeny głównej**
- [ ] **Google Analytics pokazuje wizyty**
- [ ] **Google Search Console jest zweryfikowana**
- [ ] **Sitemap jest przesłany**

## 🔧 **ROZWIĄZYWANIE PROBLEMÓW**

### **Problem: Domena nie działa**
- Sprawdź czy DNS jest poprawnie skonfigurowany
- Sprawdź status w Vercel dashboard
- Użyj narzędzia [dnschecker.org](https://dnschecker.org) aby sprawdzić propagację

### **Problem: HTTPS nie działa**
- Vercel automatycznie konfiguruje HTTPS
- Może potrzebować do 24h na pełną konfigurację
- Sprawdź czy nie ma przekierowań HTTP

### **Problem: Strona działa, ale obrazy nie**
- Sprawdź czy wszystkie ścieżki do obrazów są relatywne
- Sprawdź Network tab w DevTools

## 🎉 **GRATULACJE!**

Po ukończeniu tych kroków będziesz mieć:
- ✅ **Profesjonalną stronę pod własną domeną**
- ✅ **HTTPS automatycznie skonfigurowane**
- ✅ **CDN dla szybkiego ładowania globalnie**
- ✅ **Automatyczne deploye z GitHub**
- ✅ **SEO zoptymalizowane dla własnej domeny**

**Twoja strona będzie w pełni profesjonalna i gotowa do przyciągania klientów!** 🌟

---

## 📝 **NOTATKI DODATKOWE**

### **Przekierowanie z www na domenę główną**

Vercel automatycznie skonfiguruje przekierowanie z `www.bakasana-travel.blog` na `bakasana-travel.blog` (lub odwrotnie, zależnie od Twoich preferencji).

### **Subdomain Redirect**

Jeśli chcesz dodać subdomeny (np. `blog.bakasana-travel.blog`), możesz to zrobić w Vercel:
1. Settings → Domains
2. Add → `blog.bakasana-travel.blog`
3. Skonfiguruj DNS tak samo jak dla domeny głównej

### **Monitoring domeny**

Vercel oferuje monitoring domeny i automatycznie powiadomi Cię, jeśli coś przestanie działać.

### **Automatyczne odnowienie SSL**

Certyfikaty SSL są automatycznie odnawiane przez Vercel - nie musisz o tym pamiętać.

---

**Powodzenia z konfiguracją własnej domeny!** 🚀
