# 🗺️ MAPBOX SETUP - INTERAKTYWNA MAPA BALI

## 🎯 **CEL: PROFESJONALNA MAPA Z PINAMI MIEJSC**

Stworzysz interaktywną mapę Bali z pinami miejsc, które odwiedzasz podczas retreatów.

---

## 🚀 **KROK 1: ZAŁÓŻ KONTO MAPBOX**

### **Rejestracja (DARMOWE do 50,000 map loads/miesiąc):**
1. Id<PERSON> na: https://www.mapbox.com
2. Kliknij **"Get started for free"**
3. Zarejestruj się emailem: `<EMAIL>`
4. Potwierdź email

### **Podstawowa konfiguracja:**
1. **Company**: Bali Yoga Journey
2. **Use case**: Web application
3. **Industry**: Travel & Tourism

---

## 🔑 **KROK 2: POBIERZ ACCESS TOKEN**

### **W Mapbox dashboard:**
1. Id<PERSON> do **"Account" → "Access tokens"**
2. <PERSON><PERSON><PERSON><PERSON>j **Default public token** (z<PERSON><PERSON><PERSON> się od `pk.`)
3. Lub stwórz nowy token:
   - <PERSON><PERSON><PERSON>j **"Create a token"**
   - Nazwa: `Bali Yoga Journey Website`
   - Scopes: Pozostaw domyślne
   - URL restrictions: Dodaj `bakasana-travel.blog` i `localhost:3000`

### **Przykład tokena:**
```
pk.eyJ1IjoiYmFrYXNhbmEiLCJhIjoiY2xrOXZrZzlmMDAwMDNvcGZkYWY1ZGVjYSJ9.example
```

---

## ⚙️ **KROK 3: KONFIGURACJA ŚRODOWISKA**

### **Dodaj do `.env.local`:**
```bash
# Mapbox Configuration
NEXT_PUBLIC_MAPBOX_TOKEN=pk.eyJ1IjoiYmFrYXNhbmEiLCJhIjoiY2xrOXZrZzlmMDAwMDNvcGZkYWY1ZGVjYSJ9.example
```

### **Dodaj do `.env.example`:**
```bash
# Mapbox Maps
NEXT_PUBLIC_MAPBOX_TOKEN=your_mapbox_public_token
```

**UWAGA**: Token musi zaczynać się od `NEXT_PUBLIC_` żeby działał w przeglądarce!

---

## 🎨 **KROK 4: DOSTOSUJ STYLE MAPY**

### **Opcja A: Użyj gotowego stylu (NAJSZYBSZE)**
W `InteractiveMap.jsx` zmień `mapStyle`:
```jsx
// Satellite z drogami (obecny)
mapStyle="mapbox://styles/mapbox/satellite-streets-v12"

// Inne opcje:
mapStyle="mapbox://styles/mapbox/streets-v12"        // Klasyczna mapa
mapStyle="mapbox://styles/mapbox/outdoors-v12"       // Outdoor/hiking
mapStyle="mapbox://styles/mapbox/light-v11"          // Jasna minimalistyczna
mapStyle="mapbox://styles/mapbox/dark-v11"           // Ciemna
```

### **Opcja B: Stwórz własny styl (ZAAWANSOWANE)**
1. W Mapbox Studio idź do **"Styles"**
2. Kliknij **"New style"**
3. Wybierz template (np. "Outdoors")
4. Dostosuj kolory do brandu:
   - Primary: `#8B7355` (temple)
   - Secondary: `#D4AF37` (golden)
   - Water: `#3B82F6` (blue)
5. Zapisz i skopiuj Style URL

---

## 📍 **KROK 5: DODAJ WIĘCEJ MIEJSC**

### **W `InteractiveMap.jsx` dodaj nowe lokalizacje:**
```jsx
const baliLocations = [
  // Istniejące miejsca...
  
  // Nowe miejsca:
  {
    id: 'nusa-penida',
    name: 'Nusa Penida',
    coordinates: [115.5444, -8.7204],
    type: 'island',
    description: 'Dzika wyspa z dramatycznymi klifami i plażami',
    image: '/images/gallery/nusa-penida.webp',
    highlights: [
      'Kelingking Beach',
      'Angel\'s Billabong',
      'Broken Beach',
      'Crystal Bay'
    ],
    yogaSpots: [
      'Cliff yoga',
      'Beach meditation',
      'Sunrise practice'
    ]
  },
  {
    id: 'mount-batur',
    name: 'Mount Batur',
    coordinates: [115.3742, -8.2421],
    type: 'nature',
    description: 'Aktywny wulkan z trekkingiem na wschód słońca',
    image: '/images/gallery/mount-batur.webp',
    highlights: [
      'Sunrise trekking',
      'Jezioro wulkaniczne',
      'Gorące źródła',
      'Widoki na Bali'
    ],
    yogaSpots: [
      'Summit meditation',
      'Volcanic yoga',
      'Hot springs relaxation'
    ]
  }
];
```

---

## 🖼️ **KROK 6: DODAJ ZDJĘCIA MIEJSC**

### **Struktura folderów:**
```
public/images/gallery/
  ubud-rice-terraces.webp
  uluwatu-temple.webp
  canggu-beach.webp
  jatiluwih-terraces.webp
  sekumpul-waterfall.webp
  gili-air.webp
  nusa-penida.webp
  mount-batur.webp
```

### **Optymalizacja zdjęć:**
- **Format**: WebP (lepsze niż JPG)
- **Rozmiar**: 800x600px
- **Jakość**: 80-85%
- **Wielkość pliku**: Max 200KB

### **Narzędzia do konwersji:**
- **Online**: TinyPNG, Squoosh.app
- **Bulk**: ImageOptim (Mac), RIOT (Windows)

---

## 🎯 **KROK 7: DODAJ MAPĘ DO NAWIGACJI**

### **W `navigationLinks.js`:**
```jsx
export const navigationLinks = [
  { href: '/', label: 'Strona główna' },
  { href: '/blog', label: 'Blog' },
  { href: '/program', label: 'Program' },
  { href: '/mapa', label: 'Mapa Bali' },        // ← DODAJ TO
  { href: '/rezerwacja', label: 'Rezerwacja' },
  // ... reszta
];
```

---

## 📱 **KROK 8: MOBILE OPTIMIZATION**

### **Responsive design:**
Mapa automatycznie dostosowuje się do mobile, ale możesz dodać:

```jsx
// W InteractiveMap.jsx
const [isMobile, setIsMobile] = useState(false);

useEffect(() => {
  setIsMobile(window.innerWidth < 768);
}, []);

// Różne ustawienia dla mobile
const mobileViewState = {
  longitude: 115.2191,
  latitude: -8.6405,
  zoom: 8  // Mniejszy zoom na mobile
};
```

---

## 🔧 **KROK 9: ZAAWANSOWANE FUNKCJE**

### **A) Clustering (grupowanie pinów):**
```jsx
import { Cluster } from 'react-map-gl';

// Grupuj piny gdy są blisko siebie
<Cluster>
  {baliLocations.map(location => (
    <Marker key={location.id} ... />
  ))}
</Cluster>
```

### **B) Routing (trasy między miejscami):**
```jsx
// Dodaj linię pokazującą trasę retreatu
const routeCoordinates = [
  [115.2624, -8.5069], // Ubud
  [115.1333, -8.3667], // Jatiluwih
  [115.0847, -8.8290], // Uluwatu
  [116.0833, -8.3500]  // Gili Air
];
```

### **C) 3D terrain:**
```jsx
mapStyle="mapbox://styles/mapbox/satellite-streets-v12"
terrain={{ source: 'mapbox-dem', exaggeration: 1.5 }}
```

---

## 📊 **KROK 10: ANALYTICS I MONITORING**

### **Mapbox Analytics:**
W dashboard możesz śledzić:
- ✅ Liczbę map loads
- ✅ Najpopularniejsze lokalizacje
- ✅ Użycie API
- ✅ Performance

### **Google Analytics events:**
```jsx
// Dodaj tracking do InteractiveMap.jsx
const onMarkerClick = useCallback((location) => {
  // Existing code...
  
  // Track click
  gtag('event', 'map_location_click', {
    'event_category': 'engagement',
    'event_label': location.name
  });
}, []);
```

---

## ✅ **CHECKLIST IMPLEMENTACJI**

- [ ] Konto Mapbox założone
- [ ] Access token pobrany i dodany do .env.local
- [ ] Mapa wyświetla się poprawnie
- [ ] Wszystkie piny są widoczne
- [ ] Kliknięcie na pin otwiera popup
- [ ] Zdjęcia miejsc dodane i zoptymalizowane
- [ ] Mapa dodana do nawigacji
- [ ] Testowanie na mobile
- [ ] Sprawdzenie performance

---

## 💰 **KOSZTY I LIMITY**

### **Mapbox Free Tier:**
- ✅ **50,000 map loads/miesiąc** - wystarczy na start
- ✅ **Unlimited requests** do API
- ✅ **Wszystkie style map**
- ✅ **Custom styling**

### **Gdy przekroczysz limit:**
- **$5/10,000 dodatkowych loads**
- Dla małej strony to bardzo rzadko

### **Monitoring użycia:**
Sprawdzaj w dashboard czy nie zbliżasz się do limitu.

---

## 🚨 **TROUBLESHOOTING**

### **Problem: Mapa się nie ładuje**
- Sprawdź czy token jest poprawny
- Sprawdź czy zaczyna się od `NEXT_PUBLIC_`
- Sprawdź console w przeglądarce

### **Problem: Piny nie są widoczne**
- Sprawdź współrzędne (longitude, latitude)
- Sprawdź czy są w zakresie Bali
- Sprawdź zoom level

### **Problem: Zdjęcia się nie ładują**
- Sprawdź ścieżki do plików
- Sprawdź czy pliki istnieją w `/public/images/gallery/`
- Sprawdź format (WebP może nie działać w starych przeglądarkach)

### **Problem: Wolne ładowanie**
- Zoptymalizuj zdjęcia (max 200KB)
- Użyj lazy loading
- Zmniejsz liczbę pinów na mobile

---

## 🎉 **GOTOWE!**

Po konfiguracji będziesz mieć:
- ✅ **Profesjonalną mapę Bali** z satelitarnym widokiem
- ✅ **Interaktywne piny** z opisami miejsc
- ✅ **Zdjęcia i szczegóły** każdej lokalizacji
- ✅ **Mobile-friendly** design
- ✅ **SEO-optimized** strona `/mapa`

**To znacznie podniesie profesjonalizm Twojej strony! 🗺️**

---

## 🔮 **PRZYSZŁE ULEPSZENIA**

1. **Video backgrounds** w popup miejscach
2. **360° photos** z lokalizacji
3. **Weather widget** dla każdego miejsca
4. **Live tracking** podczas retreatu
5. **User reviews** dla miejsc
6. **Booking integration** - klik na pin → rezerwacja

**Mapa może stać się główną atrakcją Twojej strony! 🚀**
