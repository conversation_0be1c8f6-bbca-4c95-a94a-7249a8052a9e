# 🌸 Przewodnik Stylistyczny - Delikatny i Łagodny Design

## ✨ **Filozofia Designu**

Cały projekt został przeprojektowany w kierunku **delikatnego, łagodnego i eleganckiego** stylu, który odzwierciedla spokojną naturę jogi i piękno Bali.

## 🎨 **Kluczowe Zasady Stylistyczne**

### **1. Typografia - Lekka i Naturalna**
- **Font-weight: 300** (font-light) dla wszystkich elementów
- **Letter-spacing: -0.01em** (zmniejszony z -0.02em)
- **Line-height: 1.3** (bardziej naturalna wysokość linii)
- **Brak ciężkich stylów** - bez bold, bez uppercase tracking

### **2. Kolory - Delikatne i Wtopione**
- **Główny tekst:** `text-wood-light/85` (zamiast pełnej intensywności)
- **Nagłówki:** `text-temple` (bez /90 czy /80)
- **Tekst pomocniczy:** `text-wood-light/80`
- **Linki:** `text-temple/80 hover:text-temple`

### **3. Tła i Kontenery - Minimalne**
- **Karty:** `bg-shell/60` (zamiast /95)
- **Borders:** `border-temple/5` (bardzo delikatne)
- **Shadows:** `0 2px 8px rgba(0, 0, 0, 0.02)` (ultra-subtelne)
- **Hover:** `translateY(-1px)` (zamiast -4px)

### **4. Przyciski - Proste i Eleganckie**
- **Główne:** `bg-temple/90 text-rice font-light`
- **Drugorzędne:** `text-temple/80 hover:text-temple font-light`
- **Brak:** rounded corners, scale effects, ciężkich cieni
- **Transitions:** tylko `transition-colors duration-300`

### **5. Layouty - Przestronne i Naturalne**
- **Max-width:** `max-w-5xl` (zamiast 6xl czy 7xl)
- **Gaps:** `gap-8` lub `gap-12` (zamiast 16)
- **Padding:** `py-16` (zamiast py-20 czy py-24)
- **Margins:** `mb-20` (zamiast mb-24)

## 📄 **Zaktualizowane Strony**

### **✅ Zajęcia Online (`/zajecia-online`)**
- Hero section: uproszczony, bez dekoracji
- Sekcje: lekka typografia, minimalne borders
- Call to Action: wtopiony w tło, bez gradientów

### **✅ O Mnie (`/o-mnie`)**
- Dopasowany do stylu zajęć online
- Usunięte badge'y i floating elementy
- Lekkie kolory i font-light

### **✅ Program (`/program`)**
- Uproszczone nagłówki sekcji
- Delikatne bullet points
- Minimalistyczny Call to Action

### **✅ Strona Główna (`/`)**
- Lżejsze karty i hover effects
- Delikatniejsze kolory tekstu
- Uproszczone przyciski

## 🎯 **Komponenty UI**

### **Przyciski (Button Component)**
```jsx
// Główny przycisk
className="px-6 py-2 bg-temple/90 text-rice font-light hover:bg-temple transition-colors duration-300"

// Link tekstowy
className="text-temple/80 hover:text-temple transition-colors duration-300 font-light"
```

### **Karty (Card Component)**
```jsx
className="bg-shell/60 backdrop-blur-sm border-temple/5 hover:border-temple/10 transition-all duration-300"
```

### **Nagłówki**
```jsx
// H1
className="text-4xl md:text-5xl font-serif text-temple font-light"

// H2
className="text-3xl font-serif text-temple font-light"

// H3
className="text-lg font-serif text-temple/85 font-light"
```

### **Tekst**
```jsx
// Główny tekst
className="text-wood-light/85 leading-relaxed font-light"

// Tekst pomocniczy
className="text-wood-light/80 font-light"
```

## 🚫 **Czego Unikać**

### **Ciężkie Elementy:**
- ❌ `font-bold`, `font-semibold`
- ❌ `uppercase`, `tracking-wide`
- ❌ `rounded-3xl`, `rounded-full`
- ❌ `scale-105`, `hover:scale-*`
- ❌ Ciężkie cienie i gradienty
- ❌ Badge'y z emoji i dekoracjami

### **Zbyt Wyraziste Kolory:**
- ❌ Pełna intensywność kolorów bez opacity
- ❌ Kontrastowe tła
- ❌ Wyraźne borders

## ✨ **Efekt Końcowy**

Strona teraz ma:
- **Spokojny i elegancki** wygląd
- **Delikatne przejścia** i interakcje
- **Naturalne kolory** i typografię
- **Minimalistyczne** ale charakterystyczne elementy
- **Spójność** w całym projekcie

Wszystko jest **lekkie, delikatne i łagodne** - jak oddech jogi na Bali. 🌸
