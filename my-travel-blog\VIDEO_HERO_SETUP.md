# 🎬 VIDEO HERO BACKGROUND - INSTRUKCJA

## 🎯 **CEL: ZAMIEŃ ZDJĘCIE NA VIDEO W HERO SECTION**

Zamiast statycznego zdjęcia, dodamy piękne video z Bali jako tło hero section.

---

## 📹 **KROK 1: PRZYGOTUJ VIDEO**

### **Opcja A: U<PERSON><PERSON>j gotowego video (NAJSZYBSZE)**
1. Pobierz darmowe video z Bali z:
   - **Pexels**: https://www.pexels.com/search/videos/bali/
   - **Pixabay**: https://pixabay.com/videos/search/bali/
   - **Unsplash**: https://unsplash.com/s/videos/bali

### **Opcja B: Nagraj własne video**
1. **Długość**: 15-30 sekund (loop)
2. **Roz<PERSON><PERSON><PERSON>czość**: 1920x1080 (Full HD)
3. **Format**: MP4 (H.264)
4. **Rozmiar**: Max 5MB (kompresja!)
5. **Treść**: Spokojne ujęcia z Bali (tarasy ryżowe, ocean, świątynie)

### **Kompresja video:**
```bash
# Użyj HandBrake lub online compressor
# Cel: 5MB lub mniej
# Jakość: Medium (wystarczy dla tła)
```

---

## 📁 **KROK 2: DODAJ VIDEO DO PROJEKTU**

1. Umieść video w folderze: `public/videos/`
2. Nazwa pliku: `bali-hero.mp4`
3. Dodaj też fallback image: `bali-hero-poster.webp`

```
public/
  videos/
    bali-hero.mp4          ← Główne video
    bali-hero-poster.webp  ← Fallback image
```

---

## 💻 **KROK 3: STWÓRZ KOMPONENT VIDEO HERO**

Stwórz nowy plik: `src/components/VideoHero.jsx`

```jsx
'use client';

import { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';

export default function VideoHero({ children, fallbackImage }) {
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const videoRef = useRef(null);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadedData = () => {
      setIsVideoLoaded(true);
    };

    const handleError = () => {
      setHasError(true);
    };

    video.addEventListener('loadeddata', handleLoadedData);
    video.addEventListener('error', handleError);

    return () => {
      video.removeEventListener('loadeddata', handleLoadedData);
      video.removeEventListener('error', handleError);
    };
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Video Background */}
      {!hasError && (
        <motion.video
          ref={videoRef}
          autoPlay
          muted
          loop
          playsInline
          className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-1000 ${
            isVideoLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          poster={fallbackImage}
          initial={{ scale: 1.1 }}
          animate={{ scale: 1 }}
          transition={{ duration: 10, ease: "easeOut" }}
        >
          <source src="/videos/bali-hero.mp4" type="video/mp4" />
        </motion.video>
      )}

      {/* Fallback Image */}
      {(!isVideoLoaded || hasError) && (
        <div
          className="absolute inset-0 w-full h-full bg-cover bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${fallbackImage})` }}
        />
      )}

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-black/20 to-black/40" />

      {/* Content */}
      <div className="relative z-10 text-center text-white px-4 max-w-4xl mx-auto">
        {children}
      </div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/70"
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
        </svg>
      </motion.div>
    </section>
  );
}
```

---

## 🔄 **KROK 4: ZAKTUALIZUJ HERO SECTION**

W pliku `src/app/page.jsx` zamień `ParallaxHero` na `VideoHero`:

```jsx
// Dodaj import
import VideoHero from '@/components/VideoHero';

// Zamień HeroSection na:
const HeroSection = () => {
  return (
    <VideoHero fallbackImage="/images/background/bali-hero.webp">
      <div className="space-y-16">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.5 }}
          className="subtitle mb-8 text-xs uppercase tracking-[0.3em] opacity-60"
        >
          Retreaty Jogowe
        </motion.div>

        <motion.h1
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1.2, delay: 0.7 }}
          className="text-4xl md:text-5xl lg:text-6xl font-serif font-light mb-12 tracking-tight leading-[0.95]"
        >
          Bali Yoga<br />Journey
        </motion.h1>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 1.2 }}
          className="space-y-8"
        >
          <p className="text-lg max-w-md mx-auto leading-relaxed font-light opacity-80">
            Harmonia ducha i tropikalnej przygody
          </p>

          <div className="flex items-center justify-center">
            <div className="h-px w-16 bg-gradient-to-r from-transparent via-current to-transparent opacity-30" />
          </div>

          <Link
            href="#journey-inspiration"
            className="group inline-block text-sm uppercase tracking-[0.2em] font-light relative pb-1 border-b border-transparent hover:border-current transition-all duration-500"
          >
            Odkryj Podróż
            <div className="absolute bottom-0 left-0 w-0 h-px bg-current transition-all duration-500 group-hover:w-full"></div>
          </Link>
        </motion.div>
      </div>
    </VideoHero>
  );
};
```

---

## ⚡ **KROK 5: OPTYMALIZACJA PERFORMANCE**

### **Lazy Loading dla mobile:**
```jsx
// W VideoHero.jsx dodaj:
const [shouldLoadVideo, setShouldLoadVideo] = useState(false);

useEffect(() => {
  // Ładuj video tylko na desktop lub po interakcji
  const isDesktop = window.innerWidth >= 768;
  const hasGoodConnection = navigator.connection?.effectiveType === '4g';
  
  if (isDesktop || hasGoodConnection) {
    setShouldLoadVideo(true);
  }
}, []);
```

### **Preload hints:**
```jsx
// W layout.jsx dodaj do <head>:
<link rel="preload" href="/videos/bali-hero.mp4" as="video" type="video/mp4" />
```

---

## 🎨 **EFEKTY WIZUALNE**

### **Parallax na video:**
```jsx
const [scrollY, setScrollY] = useState(0);

useEffect(() => {
  const handleScroll = () => setScrollY(window.scrollY);
  window.addEventListener('scroll', handleScroll);
  return () => window.removeEventListener('scroll', handleScroll);
}, []);

// W video style:
style={{
  transform: `translateY(${scrollY * 0.5}px)`,
}}
```

### **Particle overlay (opcjonalnie):**
```jsx
// Dodaj subtelne cząsteczki nad video
<div className="absolute inset-0 bg-[url('/images/particles.png')] opacity-10 animate-pulse" />
```

---

## 📱 **MOBILE OPTIMIZATION**

```jsx
// Wyłącz video na słabych połączeniach
const shouldShowVideo = useMemo(() => {
  if (typeof window === 'undefined') return false;
  
  const connection = navigator.connection;
  const isSlowConnection = connection?.effectiveType === '2g' || connection?.effectiveType === '3g';
  const isMobile = window.innerWidth < 768;
  
  return !isSlowConnection && !isMobile;
}, []);
```

---

## ✅ **CHECKLIST**

- [ ] Video przygotowane (max 5MB)
- [ ] Fallback image gotowy
- [ ] VideoHero komponent stworzony
- [ ] Hero section zaktualizowany
- [ ] Testowane na mobile
- [ ] Testowane na desktop
- [ ] Performance sprawdzone

---

## 🚀 **REZULTAT**

Po implementacji będziesz mieć:
- ✅ Piękne video tło z Bali
- ✅ Smooth animacje tekstu
- ✅ Fallback na image jeśli video nie załaduje
- ✅ Optymalizacja dla mobile
- ✅ Parallax effect
- ✅ Professional look & feel

**To da natychmiastowy "WOW" efekt! 🎬**

---

## 🆘 **TROUBLESHOOTING**

### Video nie gra:
- Sprawdź czy plik istnieje w `/public/videos/`
- Sprawdź format (MP4 H.264)
- Sprawdź rozmiar (max 5MB)

### Wolne ładowanie:
- Kompresuj video bardziej
- Dodaj `preload="metadata"`
- Użyj CDN (Cloudinary)

### Mobile issues:
- Dodaj `playsInline` attribute
- Sprawdź autoplay policy
- Użyj fallback image

**Powodzenia! 🎯**
