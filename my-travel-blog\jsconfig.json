{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "jsx": "preserve", "module": "esnext", "moduleResolution": "node", "target": "es2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "plugins": [{"name": "next"}]}, "include": ["src/**/*", "next-env.d.ts"], "exclude": ["node_modules"]}