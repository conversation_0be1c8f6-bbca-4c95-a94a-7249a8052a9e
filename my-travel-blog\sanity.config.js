// sanity.config.js
import {defineConfig} from 'sanity'
import {deskTool} from 'sanity/desk'
import {visionTool} from '@sanity/vision'
import {schemaTypes} from './sanity/schemas'

export default defineConfig({
  name: 'bakasana-travel',
  title: 'Bakasana Travel - CMS',
  
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'your-project-id',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  
  plugins: [
    deskTool({
      structure: (S) =>
        S.list()
          .title('Zawartość')
          .items([
            // Retreaty
            S.listItem()
              .title('🧘‍♀️ Retreaty Jogowe')
              .child(
                S.documentTypeList('retreat')
                  .title('Retreaty Jogowe')
                  .filter('_type == "retreat"')
              ),
            
            // Opinie
            S.listItem()
              .title('⭐ Opinie uczestników')
              .child(
                S.documentTypeList('testimonial')
                  .title('Opinie uczestników')
                  .filter('_type == "testimonial"')
              ),
            
            // FAQ
            S.listItem()
              .title('❓ FAQ')
              .child(
                S.documentTypeList('faq')
                  .title('Często zadawane pytania')
                  .filter('_type == "faq"')
              ),
            
            S.divider(),
            
            // Blog
            S.listItem()
              .title('📝 Blog')
              .child(
                S.list()
                  .title('Blog')
                  .items([
                    S.listItem()
                      .title('Artykuły')
                      .child(
                        S.documentTypeList('blogPost')
                          .title('Artykuły')
                      ),
                    S.listItem()
                      .title('Autorzy')
                      .child(
                        S.documentTypeList('author')
                          .title('Autorzy')
                      ),
                    S.listItem()
                      .title('Kategorie')
                      .child(
                        S.documentTypeList('category')
                          .title('Kategorie')
                      ),
                  ])
              ),
            
            S.divider(),
            
            // Ustawienia
            S.listItem()
              .title('⚙️ Ustawienia strony')
              .child(
                S.document()
                  .schemaType('siteSettings')
                  .documentId('siteSettings')
                  .title('Ustawienia strony')
              ),
          ])
    }),
    visionTool()
  ],
  
  schema: {
    types: schemaTypes,
  },
  
  // Konfiguracja dokumentów
  document: {
    // Usuń opcje publikowania dla niektórych typów
    actions: (prev, context) => {
      if (context.schemaType === 'siteSettings') {
        return prev.filter(({action}) => !['unpublish', 'delete', 'duplicate'].includes(action))
      }
      return prev
    }
  }
})
