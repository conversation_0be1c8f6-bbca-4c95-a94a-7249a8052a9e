// sanity/schemas/retreat.js
export default {
  name: 'retreat',
  title: 'Retreaty Jogowe',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Tytuł retreatu',
      type: 'string',
      validation: Rule => Rule.required()
    },
    {
      name: 'slug',
      title: 'URL (slug)',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: Rule => Rule.required()
    },
    {
      name: 'shortDescription',
      title: 'Krótki opis (dla kart)',
      type: 'text',
      rows: 3,
      validation: Rule => Rule.max(200)
    },
    {
      name: 'description',
      title: 'Pełny opis',
      type: 'array',
      of: [
        {
          type: 'block'
        }
      ]
    },
    {
      name: 'startDate',
      title: 'Data rozpoczęcia',
      type: 'date',
      validation: Rule => Rule.required()
    },
    {
      name: 'endDate',
      title: 'Data zakończenia',
      type: 'date',
      validation: Rule => Rule.required()
    },
    {
      name: 'price',
      title: '<PERSON><PERSON> (PLN)',
      type: 'number',
      validation: Rule => Rule.required().min(0)
    },
    {
      name: 'currency',
      title: 'Waluta',
      type: 'string',
      options: {
        list: [
          {title: 'PLN', value: 'PLN'},
          {title: 'EUR', value: 'EUR'},
          {title: 'USD', value: 'USD'}
        ]
      },
      initialValue: 'PLN'
    },
    {
      name: 'maxParticipants',
      title: 'Maksymalna liczba uczestników',
      type: 'number',
      validation: Rule => Rule.required().min(1).max(50)
    },
    {
      name: 'currentParticipants',
      title: 'Aktualna liczba uczestników',
      type: 'number',
      validation: Rule => Rule.min(0),
      initialValue: 0
    },
    {
      name: 'location',
      title: 'Lokalizacja',
      type: 'object',
      fields: [
        {
          name: 'country',
          title: 'Kraj',
          type: 'string',
          initialValue: 'Indonezja'
        },
        {
          name: 'region',
          title: 'Region',
          type: 'string',
          initialValue: 'Bali'
        },
        {
          name: 'places',
          title: 'Miejsca do odwiedzenia',
          type: 'array',
          of: [{type: 'string'}]
        }
      ]
    },
    {
      name: 'highlights',
      title: 'Najważniejsze atrakcje',
      type: 'array',
      of: [{type: 'string'}]
    },
    {
      name: 'included',
      title: 'Co jest wliczone w cenę',
      type: 'array',
      of: [{type: 'string'}]
    },
    {
      name: 'notIncluded',
      title: 'Co NIE jest wliczone',
      type: 'array',
      of: [{type: 'string'}]
    },
    {
      name: 'images',
      title: 'Zdjęcia',
      type: 'array',
      of: [
        {
          type: 'image',
          options: {
            hotspot: true
          },
          fields: [
            {
              name: 'alt',
              title: 'Tekst alternatywny',
              type: 'string'
            },
            {
              name: 'caption',
              title: 'Podpis',
              type: 'string'
            }
          ]
        }
      ]
    },
    {
      name: 'featured',
      title: 'Wyróżniony retreat',
      type: 'boolean',
      initialValue: false
    },
    {
      name: 'status',
      title: 'Status',
      type: 'string',
      options: {
        list: [
          {title: 'Dostępny', value: 'available'},
          {title: 'Ostatnie miejsca', value: 'last_spots'},
          {title: 'Wyprzedany', value: 'sold_out'},
          {title: 'Anulowany', value: 'cancelled'},
          {title: 'Ukryty', value: 'hidden'}
        ]
      },
      initialValue: 'available'
    },
    {
      name: 'seo',
      title: 'SEO',
      type: 'object',
      fields: [
        {
          name: 'metaTitle',
          title: 'Meta tytuł',
          type: 'string',
          validation: Rule => Rule.max(60)
        },
        {
          name: 'metaDescription',
          title: 'Meta opis',
          type: 'text',
          validation: Rule => Rule.max(160)
        },
        {
          name: 'keywords',
          title: 'Słowa kluczowe',
          type: 'array',
          of: [{type: 'string'}]
        }
      ]
    }
  ],
  preview: {
    select: {
      title: 'title',
      startDate: 'startDate',
      price: 'price',
      status: 'status',
      media: 'images.0'
    },
    prepare(selection) {
      const {title, startDate, price, status} = selection
      const statusLabels = {
        available: '✅ Dostępny',
        last_spots: '⚠️ Ostatnie miejsca',
        sold_out: '❌ Wyprzedany',
        cancelled: '🚫 Anulowany',
        hidden: '👁️ Ukryty'
      }
      
      return {
        title: title,
        subtitle: `${startDate} | ${price} PLN | ${statusLabels[status] || status}`,
        media: selection.media
      }
    }
  }
}
