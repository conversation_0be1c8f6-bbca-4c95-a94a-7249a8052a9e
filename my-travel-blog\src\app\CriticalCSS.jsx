'use client';

import { useEffect, useState } from 'react';

export default function CriticalCSS() {
  const [fontsLoaded, setFontsLoaded] = useState(false);

  useEffect(() => {
    try {
      // Sprawdź, czy czcionki zostały załadowane
      if (typeof document !== 'undefined' && document.fonts) {
        document.fonts.ready.then(() => {
          setFontsLoaded(true);
        }).catch(err => {
          console.error('Błąd ładowania czcionek:', err);
          setFontsLoaded(true); // Ustaw na true mimo błędu, aby nie blokować innych operacji
        });
      } else {
        // Fallback jeśli API document.fonts nie jest dostępne
        setFontsLoaded(true);
      }

      // Opóźnij ładowanie niekrytycznych zasobów
      const loadNonCriticalResources = () => {
        try {
          // Załaduj niekrytyczne style
          const nonCriticalStyles = document.querySelectorAll('link[data-non-critical="true"]');
          nonCriticalStyles.forEach(link => {
            link.media = 'all';
          });

          // Załaduj niekrytyczne skrypty
          const nonCriticalScripts = document.querySelectorAll('script[data-non-critical="true"]');
          nonCriticalScripts.forEach(script => {
            const newScript = document.createElement('script');
            [...script.attributes].forEach(attr => {
              if (attr.name !== 'data-non-critical') {
                newScript.setAttribute(attr.name, attr.value);
              }
            });
            newScript.innerHTML = script.innerHTML;
            script.parentNode.replaceChild(newScript, script);
          });
        } catch (error) {
          console.error('Błąd podczas ładowania niekrytycznych zasobów:', error);
        }
      };

      // Użyj requestIdleCallback lub setTimeout jako fallback
      const scheduleNonCritical = () => {
        if (typeof window !== 'undefined') {
          if ('requestIdleCallback' in window) {
            window.requestIdleCallback(loadNonCriticalResources);
          } else {
            setTimeout(loadNonCriticalResources, 1000);
          }
        }
      };

      // Załaduj niekrytyczne zasoby po załadowaniu strony
      if (typeof window !== 'undefined') {
        if (document.readyState === 'complete') {
          scheduleNonCritical();
        } else {
          const handleLoad = () => scheduleNonCritical();
          window.addEventListener('load', handleLoad);
          return () => window.removeEventListener('load', handleLoad);
        }
      }
    } catch (error) {
      console.error('Błąd w komponencie CriticalCSS:', error);
    }
  }, []);

  return null;
}