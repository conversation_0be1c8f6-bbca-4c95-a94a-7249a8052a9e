import { NextResponse } from 'next/server';

// Rate limiting - proste w pamięci (w produkcji użyj <PERSON>is)
const requestCounts = new Map();
const RATE_LIMIT = 3; // 3 booking requests na IP na 15 minut
const RATE_WINDOW = 15 * 60 * 1000; // 15 minut

function getClientIP(request) {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  if (realIP) {
    return realIP;
  }

  return 'unknown';
}

function isRateLimited(ip) {
  const now = Date.now();
  const requests = requestCounts.get(ip) || [];

  // Usuń stare requesty
  const recentRequests = requests.filter(time => now - time < RATE_WINDOW);

  if (recentRequests.length >= RATE_LIMIT) {
    return true;
  }

  // Dodaj nowy request
  recentRequests.push(now);
  requestCounts.set(ip, recentRequests);

  return false;
}

export async function POST(request) {
  try {
    const clientIP = getClientIP(request);

    // Sprawdź rate limiting
    if (isRateLimited(clientIP)) {
      console.warn(`Rate limit exceeded for booking API from IP: ${clientIP}`);
      return NextResponse.json(
        {
          success: false,
          error: 'Zbyt wiele prób rezerwacji. Spróbuj ponownie za 15 minut.',
          rateLimitExceeded: true
        },
        { status: 429 }
      );
    }
    const { retreat, formData, timestamp } = await request.json();

    // Validate required fields
    if (!retreat || !formData) {
      return NextResponse.json(
        { success: false, error: 'Missing required data' },
        { status: 400 }
      );
    }

    // Validate personal data
    const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'emergencyContact', 'emergencyPhone'];
    for (const field of requiredFields) {
      if (!formData[field]?.trim()) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate email format
    if (!formData.email.includes('@')) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Calculate total price
    const basePrice = retreat.price || 0;
    const roomSupplement = formData.roomPreference === 'single' ? 500 : 0;
    const totalPrice = basePrice + roomSupplement;
    const depositAmount = Math.round(totalPrice * 0.3);

    // Generate booking reference
    const bookingRef = `BR${Date.now().toString().slice(-6)}${Math.random().toString(36).substr(2, 3).toUpperCase()}`;

    // Prepare booking data
    const bookingData = {
      bookingReference: bookingRef,
      retreat: {
        id: retreat.id,
        title: retreat.title,
        startDate: retreat.start,
        endDate: retreat.end,
        basePrice: basePrice
      },
      customer: {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        birthDate: formData.birthDate,
        emergencyContact: formData.emergencyContact,
        emergencyPhone: formData.emergencyPhone
      },
      preferences: {
        yogaExperience: formData.yogaExperience,
        dietaryRestrictions: formData.dietaryRestrictions,
        medicalConditions: formData.medicalConditions,
        roomPreference: formData.roomPreference,
        specialRequests: formData.specialRequests
      },
      pricing: {
        basePrice,
        roomSupplement,
        totalPrice,
        depositAmount,
        remainingAmount: totalPrice - depositAmount
      },
      payment: {
        method: formData.paymentMethod,
        depositPaid: false,
        fullPaymentPaid: false
      },
      agreements: {
        termsAccepted: formData.agreeTerms,
        newsletterOptIn: formData.agreeNewsletter
      },
      status: 'pending_deposit',
      createdAt: timestamp || new Date().toISOString()
    };

    // In a real application, you would:
    // 1. Save to database
    // 2. Send confirmation email
    // 3. Generate payment link
    // 4. Update retreat availability
    
    // For now, we'll simulate success and log the booking
    console.log('New booking received:', bookingData);

    // Simulate email sending
    const emailSent = await sendBookingConfirmationEmail(bookingData);

    // Add to newsletter if opted in
    if (formData.agreeNewsletter) {
      try {
        await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/newsletter`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: formData.email,
            tags: ['retreat-booking', 'customer'],
            source: 'booking-form'
          })
        });
      } catch (error) {
        console.warn('Newsletter signup failed:', error);
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Booking created successfully',
      booking: {
        reference: bookingRef,
        totalPrice,
        depositAmount,
        paymentInstructions: generatePaymentInstructions(formData.paymentMethod, depositAmount, bookingRef)
      }
    });

  } catch (error) {
    console.error('Booking error:', error);

    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process booking',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// Simulate email sending
async function sendBookingConfirmationEmail(bookingData) {
  // In production, integrate with email service (Resend, SendGrid, etc.)
  console.log('Sending booking confirmation email to:', bookingData.customer.email);
  
  const emailContent = `
    Dziękujemy za rezerwację retreatu!
    
    Numer rezerwacji: ${bookingData.bookingReference}
    Retreat: ${bookingData.retreat.title}
    Cena całkowita: ${bookingData.pricing.totalPrice} PLN
    Zadatek do wpłaty: ${bookingData.pricing.depositAmount} PLN
    
    Dane do przelewu otrzymasz w osobnym emailu.
    
    Namaste,
    Julia Jakubowicz
    Bali Yoga Journey
  `;
  
  // Simulate email sending delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  return true;
}

// Generate payment instructions based on method
function generatePaymentInstructions(method, amount, reference) {
  if (method === 'transfer') {
    return {
      type: 'bank_transfer',
      amount,
      reference,
      instructions: `
        Dane do przelewu:
        
        Odbiorca: Julia Jakubowicz
        Numer konta: 12 3456 7890 1234 5678 9012 3456
        Kwota: ${amount} PLN
        Tytuł: Zadatek retreat ${reference}
        
        Po wpłacie zadatku Twoja rezerwacja zostanie potwierdzona.
        Resztę kwoty (${amount * 2.33} PLN) wpłać 30 dni przed wyjazdem.
      `
    };
  } else if (method === 'blik') {
    return {
      type: 'blik',
      amount,
      reference,
      instructions: `
        Link do płatności BLIK zostanie wysłany na Twój email w ciągu 15 minut.
        
        Kwota zadatku: ${amount} PLN
        Numer rezerwacji: ${reference}
        
        Po wpłacie zadatku Twoja rezerwacja zostanie potwierdzona.
      `
    };
  }
  
  return {
    type: 'contact',
    instructions: 'Skontaktuj się z nami w sprawie płatności: <EMAIL>'
  };
}

// GET endpoint for testing
export async function GET() {
  return NextResponse.json({
    message: 'Booking API is working',
    timestamp: new Date().toISOString()
  });
}
