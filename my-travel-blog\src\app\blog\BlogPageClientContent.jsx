'use client';

import Link from 'next/link';
import Image from 'next/image';
import React, { useMemo } from 'react';
import OptimizedIcon from '@/components/OptimizedIcon';

// SafeIcon component - używa zoptymalizowanych ikon
const SafeIcon = ({ iconName, className = "" }) => {
  return <OptimizedIcon name={iconName} className={className} />;
};

// PostCard component
const PostCard = ({ post, featured = false }) => {
  // Używamy useMemo dla stabilnych wartości
  const formattedDate = useMemo(() => {
    if (!post?.date) return 'Data nieznana';
    try {
      return new Date(post.date).toLocaleDateString('pl-PL', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      return 'Data nieznana';
    }
  }, [post?.date]);

  const displayTags = useMemo(() => {
    return Array.isArray(post?.tags) ? post.tags.slice(0, 2) : [];
  }, [post?.tags]);

  const remainingTagsCount = useMemo(() => {
    if (!Array.isArray(post?.tags)) return 0;
    return Math.max(0, post.tags.length - 2);
  }, [post?.tags]);

  if (!post) return null;

  return (
    <article className={`group relative overflow-hidden transition-all duration-500 hover:-translate-y-2 ${
      featured
        ? 'unified-card bg-gradient-to-br from-shell/95 to-rice/90 border-2 border-golden/20 shadow-warm hover:shadow-xl'
        : 'unified-card'
    }`}>
      <Link href={`/blog/${post.slug || '#'}`} className="block h-full">
        <div className="relative overflow-hidden rounded-t-lg">
          <div className="relative w-full h-[240px]">
            <Image
              src={post.imageUrl || '/images/placeholder/image.jpg'}
              alt={post.imageAlt || post.title || 'Blog image'}
              fill
              className="object-cover transition-all duration-700 group-hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              quality={85}
              priority={featured}
            />

          </div>

          {/* Category Badge */}
          <div className="absolute top-3 left-3">
            <span className="px-3 py-1.5 bg-temple/85 text-rice text-xs font-medium uppercase tracking-wide backdrop-blur-sm rounded-full border border-rice/20">
              {post.category || 'Joga'}
            </span>
          </div>




        </div>

        <div className="p-6 flex flex-col flex-grow">
          <h2 className={`font-serif leading-tight mb-3 group-hover:text-golden transition-colors duration-300 ${
            featured ? 'text-2xl font-bold text-temple' : 'text-xl font-semibold text-temple'
          }`}>
            {post.title || 'Bez tytułu'}
          </h2>

          <p className="text-wood-light text-sm leading-relaxed mb-4 flex-grow line-clamp-3 font-light">
            {post.excerpt || ''}
          </p>

          <div className="space-y-3">
            <div className="flex items-center justify-between text-xs text-wood-light/70">
              <div className="flex items-center gap-1.5">
                <SafeIcon iconName="Calendar" className="w-3.5 h-3.5 text-temple/60" />
                <span className="font-medium">{formattedDate}</span>
              </div>
              <div className="flex items-center gap-1.5">
                <SafeIcon iconName="User" className="w-3.5 h-3.5 text-temple/60" />
                <span className="font-medium">{post.author || 'Julia'}</span>
              </div>
            </div>

            {displayTags.length > 0 && (
              <div className="flex flex-wrap gap-1.5">
                {displayTags.map((tag, index) => (
                  <span key={`tag-${index}-${tag}`} className="px-2 py-1 bg-temple/5 text-temple text-xs font-medium rounded-full border border-temple/10">
                    {tag}
                  </span>
                ))}
                {remainingTagsCount > 0 && (
                  <span className="px-2 py-1 text-wood-light/50 text-xs font-medium">
                    +{remainingTagsCount}
                  </span>
                )}
              </div>
            )}
          </div>

          {/* Read More Button */}
          <div className="mt-4 pt-3 border-t border-temple/10">
            <div className="flex items-center justify-between group/btn">
              <span className="text-temple font-medium text-sm group-hover:text-golden transition-colors">
                Czytaj więcej
              </span>
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-temple/5 group-hover:bg-golden/10 transition-all duration-300">
                <SafeIcon iconName="ArrowRight" className="w-4 h-4 text-temple group-hover:text-golden group-hover:translate-x-0.5 transition-all duration-300" />
              </div>
            </div>
          </div>
        </div>
      </Link>
    </article>
  );
};

export default function BlogPageClientContent({ posts = [] }) {
  const memoizedPosts = useMemo(() => {
    if (!Array.isArray(posts)) return [];
    return posts.filter(post => post && typeof post === 'object');
  }, [posts]);

  const featuredPosts = useMemo(() => 
    memoizedPosts.filter(post => post?.featured === true), 
    [memoizedPosts]
  );

  const regularPosts = useMemo(() => 
    memoizedPosts.filter(post => post?.featured !== true), 
    [memoizedPosts]
  );

  return (
    <div className="relative section-bg min-h-screen">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-temple/5 via-transparent to-golden/5"></div>
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-20 relative">
          <div className="text-center">

            <h1 className="text-5xl md:text-6xl font-serif text-temple tracking-tight mb-6 leading-tight font-light">
              Inspiracje z <span className="text-golden">Bali</span>
            </h1>

            <div className="decorative-line"></div>

            <p className="text-lg text-wood-light/90 max-w-2xl mx-auto leading-relaxed font-light">
              Odkryj transformacyjną moc jogi, poznaj sekrety Wyspy Bogów i zanurz się w świecie
              mindfulness i duchowego rozwoju.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        {/* Blog Posts */}
        <section className="mb-20">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {memoizedPosts.length > 0 ? (
              memoizedPosts.map((post, index) => (
                <PostCard key={`post-${post?.slug || index}`} post={post} />
              ))
            ) : (
              <div className="col-span-full text-center py-16">
                <div className="max-w-md mx-auto">
                  <SafeIcon iconName="Eye" className="w-12 h-12 text-temple/30 mx-auto mb-4" />
                  <h3 className="text-xl font-serif text-temple mb-2">Wkrótce więcej treści</h3>
                  <p className="text-wood-light/70 text-sm">Pracujemy nad nowymi inspirującymi artykułami</p>
                </div>
              </div>
            )}
          </div>
        </section>



        {/* Community Section */}
        <section className="mt-24">
          <div className="unified-card bg-gradient-to-br from-shell/60 via-rice/80 to-mist/60 p-12 text-center">
            <div className="relative">
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-temple/5 rounded-full text-temple text-sm font-medium mb-6 backdrop-blur-sm border border-temple/10">
                <SafeIcon iconName="Heart" className="w-4 h-4" />
                <span>Dołącz do społeczności</span>
              </div>

              <h3 className="text-3xl font-serif text-temple mb-4 font-light">Bądź na bieżąco</h3>
              <p className="text-wood-light text-lg mb-8 max-w-2xl mx-auto font-light">
                Otrzymuj najnowsze artykuły, inspiracje z Bali i ekskluzywne treści
                prosto na swój email
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}