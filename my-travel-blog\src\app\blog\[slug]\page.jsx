import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { blogPosts } from '@/data/blogPosts';
import OptimizedIcon from '@/components/OptimizedIcon';
import { generateMetadata } from './metadata';
import { generateBlogStructuredData } from '@/app/metadata';
import Breadcrumbs from '@/components/Breadcrumbs';

function formatDate(dateString) {
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('pl-PL', options);
}

export async function generateStaticParams() {
  return blogPosts.map((post) => ({
    slug: post.slug,
  }));
}

// Eksportuj funkcję generateMetadata z pliku metadata.js
export { generateMetadata };

export default async function PostPage({ params }) {
  try {
    const { slug } = await params;
    const post = blogPosts.find((p) => p.slug === slug);

    if (!post) {
      notFound();
    }

    const relatedPosts = post.tags
      ? blogPosts
          .filter(p => p.slug !== post.slug && p.tags?.some(tag => post.tags.includes(tag)))
          .slice(0, 3)
      : [];

    // Generuj structured data dla tego posta
    const structuredData = generateBlogStructuredData(post);

    return (
      <>
        {/* Structured Data */}
        {structuredData && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
          />
        )}

        {/* Main Content */}
      <div className="relative bg-gradient-to-b from-shell/40 via-rice/60 to-mist/30 min-h-screen">
        {/* Breadcrumbs & Navigation */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-8">
          <Breadcrumbs
            customItems={[
              { label: 'Strona główna', href: '/' },
              { label: 'Blog', href: '/blog' },
              { label: post.title, href: null }
            ]}
          />

          <Link
            href="/blog"
            className="inline-flex items-center gap-2 text-temple/70 hover:text-temple transition-colors duration-300 group mt-4"
          >
            <OptimizedIcon name="ArrowLeft" className="w-4 h-4 group-hover:-translate-x-1 transition-transform duration-300" />
            <span className="text-sm font-medium">Wróć do bloga</span>
          </Link>
        </div>

        {/* Hero Image */}
        {post.imageUrl && (
          <div className="relative h-[60vh] overflow-hidden">
            <Image
              src={post.imageUrl}
              alt={post.imageAlt || post.title}
              fill
              priority
              className="object-cover"
              sizes="100vw"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-temple/60 via-temple/20 to-transparent"></div>

            {/* Category Badge */}
            <div className="absolute top-8 left-8">
              <span className="px-4 py-2 bg-temple/90 text-rice text-sm font-medium uppercase tracking-wide backdrop-blur-sm rounded-full border border-rice/20">
                {post.category || 'Joga'}
              </span>
            </div>

            {/* Featured Badge */}
            {post.featured && (
              <div className="absolute top-8 right-8">
                <div className="flex items-center gap-2 px-3 py-2 bg-golden/90 text-rice text-sm font-medium uppercase tracking-wide backdrop-blur-sm rounded-full border border-rice/20">
                  <OptimizedIcon name="Heart" className="w-4 h-4 fill-current" />
                  <span>Polecane</span>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Article Content */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <article className="bg-shell/80 backdrop-blur-md rounded-3xl border border-temple/10 shadow-soft overflow-hidden">
            {/* Article Header */}
            <header className="p-8 pb-6 border-b border-temple/10">
              <h1 className="text-4xl md:text-5xl font-serif text-temple leading-tight mb-6">
                {post.title}
              </h1>

              <div className="flex flex-wrap items-center gap-6 text-sm text-wood-light/80 mb-6">
                <div className="flex items-center gap-2">
                  <OptimizedIcon name="Calendar" className="w-4 h-4 text-temple/60" />
                  <time dateTime={post.date} className="font-medium">
                    {formatDate(post.date)}
                  </time>
                </div>

                <div className="flex items-center gap-2">
                  <OptimizedIcon name="User" className="w-4 h-4 text-temple/60" />
                  <span className="font-medium">{post.author}</span>
                </div>

                <div className="flex items-center gap-2">
                  <OptimizedIcon name="Clock" className="w-4 h-4 text-temple/60" />
                  <span className="font-medium">{post.readTime || '5 minut'}</span>
                </div>
              </div>

              {/* Tags */}
              {post.tags && post.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {post.tags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center gap-1 px-3 py-1.5 bg-temple/5 text-temple text-xs font-medium rounded-full border border-temple/10"
                    >
                      <OptimizedIcon name="Tag" className="w-3 h-3" />
                      {tag}
                    </span>
                  ))}
                </div>
              )}
            </header>

            {/* Article Body */}
            <div className="prose prose-lg max-w-none p-8 prose-headings:text-temple prose-headings:font-serif prose-p:text-wood-light prose-p:leading-relaxed prose-a:text-temple prose-a:no-underline hover:prose-a:text-golden prose-strong:text-temple prose-blockquote:border-l-temple prose-blockquote:bg-temple/5 prose-blockquote:text-wood-light prose-ul:text-wood-light prose-ol:text-wood-light">
              <div dangerouslySetInnerHTML={{ __html: post.content }} />
            </div>
          </article>

          {/* Related Posts */}
          {relatedPosts.length > 0 && (
            <section className="mt-16">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-serif text-temple mb-2">Powiązane Artykuły</h2>
                <p className="text-wood-light/80 text-sm">Może Cię również zainteresować</p>
                <div className="w-12 h-0.5 bg-temple/20 mx-auto mt-4"></div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {relatedPosts.map(relatedPost => (
                  <Link
                    href={`/blog/${relatedPost.slug}`}
                    key={relatedPost.slug}
                    className="group bg-shell/60 backdrop-blur-sm rounded-2xl border border-temple/10 overflow-hidden hover:shadow-medium transition-all duration-300 hover:-translate-y-1"
                  >
                    {relatedPost.imageUrl && (
                      <div className="relative h-40 overflow-hidden">
                        <Image
                          src={relatedPost.imageUrl}
                          alt={relatedPost.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                          sizes="(max-width: 768px) 100vw, 33vw"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-temple/20 to-transparent"></div>
                      </div>
                    )}
                    <div className="p-4">
                      <h3 className="font-serif text-temple text-lg leading-tight group-hover:text-golden transition-colors duration-300">
                        {relatedPost.title}
                      </h3>
                      <p className="text-wood-light/70 text-sm mt-2 line-clamp-2">
                        {relatedPost.excerpt}
                      </p>
                    </div>
                  </Link>
                ))}
              </div>
            </section>
          )}

          {/* Share & Back Navigation */}
          <div className="mt-16 flex flex-col sm:flex-row items-center justify-between gap-4 p-6 bg-temple/5 rounded-2xl backdrop-blur-sm">
            <div className="flex items-center gap-4">
              <span className="text-temple font-medium text-sm">Podziel się artykułem:</span>
              <div className="flex items-center gap-2">
                <button className="p-2 bg-temple/10 hover:bg-temple/20 rounded-full transition-colors duration-300">
                  <OptimizedIcon name="Share2" className="w-4 h-4 text-temple" />
                </button>
                <button className="p-2 bg-temple/10 hover:bg-temple/20 rounded-full transition-colors duration-300">
                  <OptimizedIcon name="Heart" className="w-4 h-4 text-temple" />
                </button>
              </div>
            </div>

            <Link
              href="/blog"
              className="btn-soft flex items-center gap-2"
            >
              <OptimizedIcon name="ArrowLeft" className="w-4 h-4" />
              Wszystkie artykuły
            </Link>
          </div>
        </div>
      </div>
      </>
    );
  } catch (error) {
    notFound();
  }
}