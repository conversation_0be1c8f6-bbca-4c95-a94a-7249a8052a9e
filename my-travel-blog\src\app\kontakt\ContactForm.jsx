'use client';

import React, { useState } from 'react';
import { Instagram, Send, Facebook, CalendarCheck } from 'lucide-react';

export default function ContactForm() {
  const [formData, setFormData] = useState({ name: '', email: '', message: '', honeypot: '' });
  const [status, setStatus] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.id]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Honeypot check - jeśli wypełnione, to spam
    if (formData.honeypot) {
      return;
    }

    setIsSubmitting(true);
    setStatus('Wysyłanie...');

    try {
      const response = await fetch('https://api.web3forms.com/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          access_key: 'YOUR_WEB3FORMS_ACCESS_KEY', // Zastąp swoim kluczem z web3forms.com
          name: formData.name,
          email: formData.email,
          message: formData.message,
          subject: `Nowa wiadomość z Bali Yoga Journey od ${formData.name}`,
          from_name: 'Bali Yoga Journey',
          to_email: '<EMAIL>'
        })
      });

      const result = await response.json();

      if (result.success) {
        setStatus('✅ Wiadomość wysłana! Dziękujemy, odpowiemy wkrótce.');
        setFormData({ name: '', email: '', message: '', honeypot: '' });
      } else {
        throw new Error('Błąd wysyłania');
      }
    } catch (error) {
      console.error('Error:', error);
      setStatus('❌ Wystąpił błąd. Spróbuj ponownie lub napisz bezpoś<NAME_EMAIL>');
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setStatus(''), 8000);
    }
  };

  const socialLinks = [
    {
      href: "https://www.instagram.com/fly_with_bakasana?igsh=MWpmanNpeHVodTlubA%3D%3D&utm_source=qr",
      label: "@fly_with_bakasana",
      icon: Instagram,
      aria: "Profil na Instagramie"
    },
    {
      href: "https://www.facebook.com/p/Fly-with-bakasana-100077568306563/",
      label: "Fly with bakasana",
      icon: Facebook,
      aria: "Profil na Facebooku"
    },
    {
      href: "https://app.fitssey.com/Flywithbakasana/frontoffice",
      label: "Rezerwacje Fitssey",
      icon: CalendarCheck,
      aria: "Profil na Fitssey (rezerwacje)"
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-16 items-start">
      {/* Formularz kontaktowy */}
      <div className="unified-card p-8">
        <h2 className="text-3xl font-serif text-temple mb-6 font-light">Napisz do nas</h2>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-wood-light mb-2">Imię</label>
            <input
              type="text"
              id="name"
              value={formData.name}
              onChange={handleChange}
              required
              className="w-full px-4 py-3 bg-rice/50 border border-bamboo/20 rounded-full focus:border-temple/50 focus:ring-2 focus:ring-temple/20 focus:outline-none transition-colors text-temple"
            />
          </div>
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-wood-light mb-2">Email</label>
            <input
              type="email"
              id="email"
              value={formData.email}
              onChange={handleChange}
              required
              className="w-full px-4 py-3 bg-rice/50 border border-bamboo/20 rounded-full focus:border-temple/50 focus:ring-2 focus:ring-temple/20 focus:outline-none transition-colors text-temple"
            />
          </div>
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-wood-light mb-2">Wiadomość</label>
            <textarea
              id="message"
              value={formData.message}
              onChange={handleChange}
              required
              rows={5}
              className="w-full px-4 py-3 bg-rice/50 border border-bamboo/20 rounded-lg focus:border-temple/50 focus:ring-2 focus:ring-temple/20 focus:outline-none transition-colors text-temple resize-none"
            />
          </div>

          {/* Honeypot field - ukryte dla ludzi, widoczne dla botów */}
          <input
            type="text"
            id="honeypot"
            name="honeypot"
            value={formData.honeypot}
            onChange={handleChange}
            style={{ display: 'none' }}
            tabIndex="-1"
            autoComplete="off"
          />

          <div className="flex items-center justify-between">
            <button
              type="submit"
              disabled={isSubmitting}
              className={`btn-unified-primary group ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isSubmitting ? 'Wysyłanie...' : 'Wyślij Wiadomość'}
              <Send className={`ml-2 h-4 w-4 transition-transform ${isSubmitting ? '' : 'group-hover:translate-x-1'}`}/>
            </button>
            {status && <p className="text-sm text-temple font-medium max-w-xs">{status}</p>}
          </div>
        </form>
      </div>

      {/* Sekcja Social Media */}
      <div className="unified-card p-8">
        <h3 className="text-2xl font-serif text-temple mb-4 font-light">Połączmy siły!</h3>
        <p className="text-wood-light mb-8 leading-relaxed font-light">
          Masz pytania, sugestie, a może chcesz po prostu porozmawiać o jodze i Bali? Czekam na Twoją wiadomość! Znajdziesz nas również w mediach społecznościowych i na platformie Fitssey.
        </p>
        <h4 className="text-xl font-serif text-temple mb-5 font-light">Znajdź nas na:</h4>
        <div className="space-y-4">
          {socialLinks.map((link) => {
            const Icon = link.icon;
            return (
              <a
                key={link.label}
                href={link.href}
                target="_blank"
                rel="noopener noreferrer"
                aria-label={link.aria}
                className="flex items-center gap-3 p-3 rounded-lg transition-colors duration-300 group hover:bg-temple/5"
              >
                <Icon className="w-6 h-6 text-temple/80 group-hover:text-temple transition-colors" />
                <span className="text-base text-wood-light group-hover:text-temple transition-colors">{link.label}</span>
              </a>
            );
          })}
        </div>
      </div>
    </div>
  );
} 