import Link from 'next/link';

export const metadata = {
  title: 'Strona nie znaleziona | Fly with bakasana',
  description: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ale strona której szukasz nie istnieje.',
};

export default function NotFound() {
  return (
    <main className="py-32 min-h-screen bg-secondary flex items-center justify-center">
      <div className="max-w-2xl mx-auto px-4 text-center">
        <div className="subtitle mb-4">Błąd 404</div>

        <h1 className="text-4xl lg:text-5xl font-serif font-light mb-6">
          Strona nie została znaleziona
        </h1>

        <p className="text-lg leading-relaxed mb-8 font-light opacity-70">
          <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ale strona której szukasz nie istnieje lub została przeniesiona.
        </p>

        <div className="space-y-4">
          <Link
            href="/"
            className="btn-primary inline-block"
          >
            W<PERSON><PERSON><PERSON> do strony głównej
          </Link>

          <div className="text-sm">
            <Link
              href="/blog"
              className="text-accent hover:opacity-70 transition-opacity"
            >
              Przejdź do bloga
            </Link>
            {' · '}
            <Link
              href="/program"
              className="text-accent hover:opacity-70 transition-opacity"
            >
              Zobacz program
            </Link>
          </div>
        </div>
      </div>
    </main>
  );
}