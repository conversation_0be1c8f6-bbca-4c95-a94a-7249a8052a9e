'use client';

import { useEffect } from 'react';

export default function AsyncCSS() {
  useEffect(() => {
    // Since globals.css is already loaded by Next.js, we don't need to load it again
    // This component can be used for other non-critical CSS in the future

    // Add any additional non-critical CSS loading here if needed
    console.log('AsyncCSS: Ready for non-critical CSS loading');
  }, []);

  return null;
}
