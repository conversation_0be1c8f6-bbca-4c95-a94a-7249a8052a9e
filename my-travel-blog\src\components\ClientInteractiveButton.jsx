'use client';

import { useCallback } from 'react';
import { useRouter } from 'next/navigation';

// Simple icon placeholder to avoid import issues
const IconPlaceholder = ({ className = 'w-5 h-5' }) => (
  <div className={`${className} bg-temple/20 rounded`} />
);

export default function ClientInteractiveButton({ 
  children, 
  href, 
  onClick, 
  className = '', 
  ariaLabel,
  showIcon = true 
}) {
  const router = useRouter();

  const handleClick = useCallback(() => {
    if (onClick) {
      onClick();
    } else if (href) {
      router.push(href);
    }
  }, [onClick, href, router]);

  const scrollToSection = useCallback((sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, []);

  // Special handling for scroll buttons
  if (href === '#journey-inspiration') {
    return (
      <button
        onClick={() => scrollToSection('journey-inspiration')}
        className={className}
        aria-label={ariaLabel}
      >
        {children}
        {showIcon && <IconPlaceholder className="ml-2 h-4 w-4" />}
      </button>
    );
  }

  return (
    <button
      onClick={handleClick}
      className={className}
      aria-label={ariaLabel}
    >
      {children}
      {showIcon && <IconPlaceholder className="ml-2 h-4 w-4" />}
    </button>
  );
}
