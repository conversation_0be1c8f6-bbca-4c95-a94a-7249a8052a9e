'use client';

import React, { useEffect, useRef, useState } from 'react';
import { ArrowUp } from 'lucide-react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { socialLinks } from '@/data/contactData';
import { footerLinks } from '@/data/navigationLinks';

export default function Footer() {
  const currentYear = new Date().getFullYear();
  const footerRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (footerRef.current) {
      observer.observe(footerRef.current);
    }

    return () => {
      if (footerRef.current) {
        observer.disconnect();
      }
    };
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };



  return (
    <footer
      ref={footerRef}
      id="footer"
      className="bg-temple/5 backdrop-blur-sm border-t border-temple/10 mt-24"
    >
      <div className="max-w-6xl mx-auto px-4 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className="flex flex-col md:flex-row justify-between items-center"
        >
          <div className="flex items-center space-x-4 mb-6 md:mb-0">
            <span className="font-display text-temple/90 text-lg">Bali Yoga Journey</span>
            <span className="text-wood/40">•</span>
            <span className="text-sm text-wood/80">{currentYear}</span>
          </div>

          <div className="flex flex-wrap justify-center md:justify-end gap-x-6 gap-y-4 items-center">
            {socialLinks.map((link) => {
              const Icon = link.icon;
              return (
                <a
                  key={link.label}
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 text-sm text-wood/80 hover:text-temple transition-colors duration-300 group"
                  aria-label={link.aria}
                >
                  <Icon className="w-5 h-5 group-hover:scale-110 transition-transform" />
                  <span className="font-light">{link.label}</span>
                </a>
              );
            })}
          </div>
        </motion.div>

        <motion.nav
          initial={{ opacity: 0 }}
          animate={isVisible ? { opacity: 1 } : {}}
          transition={{ duration: 0.8, delay: 0.2, ease: 'easeOut' }}
          className="mt-10 pt-8 border-t border-temple/10"
          aria-label="Nawigacja w stopce"
        >
          <ul className="flex flex-wrap justify-center gap-x-6 gap-y-3 text-sm text-wood/80">
            {footerLinks.map((link) => (
              <li key={link.href}>
                <Link href={link.href} className="hover:text-temple transition-colors">
                  {link.label}
                </Link>
              </li>
            ))}
          </ul>
        </motion.nav>

        <div className="mt-10 flex justify-center">
          <button
            onClick={scrollToTop}
            className="p-2 rounded-full bg-temple/10 hover:bg-temple/20 transition-all duration-300"
            aria-label="Powrót do góry"
          >
            <ArrowUp className="w-6 h-6 text-temple/80" />
          </button>
        </div>
      </div>
    </footer>
  );
}