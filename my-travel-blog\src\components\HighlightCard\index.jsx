'use client';

import React from 'react';

const HighlightCard = ({ title, description, icon: Icon, index = 0 }) => {
  return (
    <article className="unified-card p-8 group animate-gentle" style={{ animationDelay: `${index * 100}ms` }}>
      <div className="flex items-center gap-4 mb-4">
        <div className="w-10 h-0.5 bg-temple/20"></div>
        <h3 className="text-xl font-serif text-temple font-light">{title}</h3>
      </div>
      <div className="flex gap-3">
        <Icon className="w-5 h-5 text-temple/60 mt-0.5 transition-transform duration-300 group-hover:scale-110" />
        <p className="text-wood-light leading-relaxed font-light">{description}</p>
      </div>
    </article>
  );
};

export default HighlightCard;