'use client';

import React, { useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { blogPosts } from '@/data/blogPosts';
import { ArrowRight, Flower2, Sunrise, Heart, MapPin, MessageSquare, BookOpen, Instagram, Facebook, CalendarCheck } from 'lucide-react';

// SafeIcon component for rendering icons with fallback
const SafeIcon = React.memo(({ Icon, className }) => {
  if (!Icon) return null;
  return <Icon className={className} />;
});
SafeIcon.displayName = 'SafeIcon';

// HeroSection bez animacji
const HeroSection = React.memo(() => {
  const scrollToCombinedSection = useCallback(() => {
    document.getElementById('journey-inspiration')?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  }, []);

  return (
    <section className="relative min-h-[80vh] md:min-h-[90vh] flex items-center justify-center w-full overflow-hidden">
      <div className="absolute inset-0 w-full h-full bg-gradient-to-b from-rice/20 via-sand-light/30 to-ocean/30">
        <div className="relative w-full h-full overflow-hidden">
          <Image
            src="/images/background/bali-hero.webp"
            alt="Bali Hero"
            fill
            priority
            className="object-cover"
            sizes="100vw"
          />
        </div>
        <div className="absolute inset-0 bg-gradient-to-t from-temple/20 to-transparent" />
      </div>
      <div className="relative z-10 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 className="text-4xl sm:text-5xl lg:text-6xl font-display text-temple tracking-wide drop-shadow-md">
          Bali Yoga Journey
        </h1>
        <p className="mt-4 text-lg sm:text-xl text-wood-light/90 max-w-2xl mx-auto leading-relaxed">
          Harmonia ducha i tropikalnej przygody z Julią Jakubowicz
        </p>
        <button
          onClick={scrollToCombinedSection}
          style={{
            marginTop: '1.5rem',
            display: 'inline-flex',
            alignItems: 'center',
            padding: '0.625rem 2rem',
            backgroundColor: 'rgba(255, 255, 255, 0.15)',
            backdropFilter: 'blur(8px)',
            color: 'rgba(90, 80, 70, 0.85)',
            border: '1px solid rgba(255, 255, 255, 0.15)',
            fontSize: '0.875rem',
            fontWeight: 'normal',
            borderRadius: '9999px',
            transition: 'all 0.3s ease',
            boxShadow: '0 2px 10px rgba(0, 0, 0, 0.02)'
          }}
          aria-label="Odkryj podróż jogową na Bali"
        >
          Odkryj Podróż
          <span className="ml-2" style={{ display: 'inline-flex', width: '16px', height: '16px' }}>
            <ArrowRight size={16} />
          </span>
        </button>
      </div>
    </section>
  );
});
HeroSection.displayName = 'HeroSection';

// Card component without animations
const Card = React.memo(({ type, title, description, link, icon: Icon, index, imageUrl }) => {
  const isHighlight = type === 'highlight';

  return (
    <div className="bg-shell/60 backdrop-blur-md border border-temple/8 shadow-soft hover:shadow-medium hover:-translate-y-1 transition-all duration-300 flex flex-col h-full overflow-hidden rounded-2xl">
      {isHighlight ? (
        <div className="p-6 flex flex-col flex-grow">
          <div className="flex items-center gap-2 mb-3">
            <SafeIcon Icon={Icon} className="w-4 h-4 text-temple/70" />
            <h3 className="text-lg font-serif text-temple/85 font-light">{title}</h3>
          </div>
          <p className="text-wood-light/75 text-sm leading-relaxed flex-grow font-light">{description}</p>
        </div>
      ) : (
        <>
          <div className="relative w-full h-[240px] overflow-hidden rounded-t-2xl">
            <div className="relative w-full h-full">
              <Image
                src={imageUrl || '/images/placeholder/image.jpg'}
                alt={title}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-103"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            </div>
          </div>
          <div className="p-5 flex flex-col flex-grow">
            <h3 className="text-lg font-serif text-temple/85 mb-2 font-light">
              <Link href={link} className="hover:text-temple/70 transition-colors">
                {title}
              </Link>
            </h3>
            <p className="text-wood-light/75 text-sm leading-relaxed mb-4 flex-grow line-clamp-3 font-light">{description}</p>
            <Link
              href={link}
              className="inline-flex items-center text-xs font-light text-temple/70 hover:text-temple transition-colors group mt-auto self-start"
              aria-label={`Przeczytaj więcej o ${title}`}
            >
              Czytaj dalej
              <SafeIcon Icon={ArrowRight} className="ml-1 h-3 w-3 group-hover:translate-x-0.5 transition-transform" />
            </Link>
          </div>
        </>
      )}
    </div>
  );
});
Card.displayName = 'Card';

const Home = ({ latestPosts }) => {
  const router = useRouter();

  const posts = useMemo(() => latestPosts || blogPosts.slice(0, 3), [latestPosts]);

  const eventData = useMemo(
    () => ({
      highlights: [
        { id: 'ubud', title: 'Ubud', description: 'Duchowe serce Bali, zanurzenie w kulturze i jodze wśród tarasów ryżowych.', icon: Flower2 },
        { id: 'gili-air', title: 'Gili Air', description: 'Rajska wyspa bez samochodów, idealna na relaks i snorkeling.', icon: Sunrise },
        { id: 'uluwatu', title: 'Uluwatu', description: 'Joga na klifach z widokiem na ocean i spektakularne zachody słońca.', icon: Heart },
      ],
    }),
    []
  );

  const combinedItems = useMemo(() => eventData.highlights.map((item) => ({ ...item, type: 'highlight' })), [eventData]);

  const socialLinks = [
    { id: 'instagram', href: 'https://www.instagram.com/fly_with_bakasana?igsh=MWpmanNpeHVodTlubA%3D%3D&utm_source=qr', label: 'Instagram', icon: Instagram, aria: 'Profil na Instagramie' },
    { id: 'facebook', href: 'https://www.facebook.com/p/Fly-with-bakasana-100077568306563/', label: 'Facebook', icon: Facebook, aria: 'Profil na Facebooku' },
    { id: 'fitssey', href: 'https://app.fitssey.com/Flywithbakasana/frontoffice', label: 'Fitssey (Rezerwacje)', icon: CalendarCheck, aria: 'Profil na Fitssey (rezerwacje)' },
  ];

  const navigateToDetails = useCallback(() => {
    router.push('/opis');
  }, [router]);

  const jsonLdString = useMemo(
    () =>
      JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'WebPage',
        name: 'Bali Yoga Journey',
        description: 'Harmonia ducha i tropikalnej przygody z Julią Jakubowicz',
        url: 'https://bali-yoga-journey.com',
        mainEntity: {
          '@type': 'Event',
          name: 'Bali Yoga Journey',
          description: 'Harmonia ducha i tropikalnej przygody z Julią Jakubowicz',
          startDate: '2025-06-01T00:00:00Z',
          endDate: '2025-06-10T00:00:00Z',
          location: {
            '@type': 'Place',
            name: 'Bali, Indonezja',
            address: {
              '@type': 'PostalAddress',
              addressLocality: 'Ubud',
              addressCountry: 'ID',
            },
          },
          organizer: {
            '@type': 'Person',
            name: 'Julia Jakubowicz',
            url: 'https://bali-yoga-journey.com/about',
          },
          image: 'https://bali-yoga-journey.com/images/background/bali-hero.webp',
        },
      }),
    []
  );

  return (
    <div className="relative bg-gradient-to-b from-rice via-sand-light/50 to-ocean/10">
      <HeroSection />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: jsonLdString }} />

      {/* Sekcja Podróż i Inspiracje */}
      <section id="journey-inspiration" className="py-20 md:py-28 bg-gradient-to-b from-rice via-sand-light/50 to-ocean/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-display text-temple tracking-tight">Podróż i Inspiracje</h2>
            <p className="mt-4 text-lg text-wood-light/80 max-w-2xl mx-auto">
              Odkryj magiczne miejsca Bali, które odwiedzimy podczas naszego retreatu jogowego
            </p>
            <div className="mt-6 w-16 h-px bg-temple/20 mx-auto" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {combinedItems.map((item, index) => (
              <Card key={item.id} type={item.type} title={item.title} description={item.description} icon={item.icon} index={index} />
            ))}
          </div>
          <div className="mt-12 text-center">
            <button
              onClick={navigateToDetails}
              className="inline-flex items-center px-6 py-3 bg-temple text-rice text-base font-medium rounded-full shadow-soft hover:bg-temple/90 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-temple/50"
              aria-label="Poznaj szczegóły programu"
            >
              Poznaj szczegóły programu
              <SafeIcon Icon={ArrowRight} className="ml-2 h-5 w-5" />
            </button>
          </div>
        </div>
      </section>

      {/* Sekcja Opinie */}
      <section className="py-24 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto bg-rice/50 rounded-3xl my-16">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-display text-temple tracking-tight">Co mówią uczestnicy</h2>
          <p className="mt-4 text-lg text-wood-light/80 max-w-2xl mx-auto">
            Poznaj opinie osób, które doświadczyły naszych retreatów jogowych na Bali
          </p>
          <div className="mt-6 w-16 h-px bg-temple/20 mx-auto" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {[
            {
              id: 'marta-k',
              name: 'Marta K.',
              text: 'Retreat z Julią to najlepsze, co mogłam dla siebie zrobić. Połączenie jogi, medytacji i eksploracji Bali było idealnie wyważone. Wróciłam odmieniona!',
              location: 'Warszawa',
            },
            {
              id: 'tomasz-w',
              name: 'Tomasz W.',
              text: 'Jako początkujący w jodze obawiałem się, czy dam radę. Julia stworzyła przestrzeń, w której każdy mógł praktykować na swoim poziomie. Bali zachwyciło mnie!',
              location: 'Kraków',
            },
            {
              id: 'karolina-m',
              name: 'Karolina M.',
              text: 'Trzeci raz uczestniczę w retreatach Julii i za każdym razem odkrywam coś nowego - zarówno w praktyce jogi, jak i w sobie. Polecam z całego serca!',
              location: 'Wrocław',
            },
          ].map((testimonial) => (
            <div
              key={testimonial.id}
              className="bg-rice/95 backdrop-blur-sm p-6 rounded-xl shadow-soft border border-bamboo/10 flex flex-col"
            >
              <div className="flex-grow">
                <p className="text-wood-light leading-relaxed mb-4">"{testimonial.text}"</p>
              </div>
              <div className="flex items-center mt-4">
                <div className="w-10 h-10 rounded-full bg-temple/10 flex items-center justify-center text-temple">
                  {testimonial.name.charAt(0)}
                </div>
                <div className="ml-3">
                  <p className="text-temple font-medium">{testimonial.name}</p>
                  <p className="text-sm text-wood-light/70">{testimonial.location}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Sekcja Blog */}
      <section className="py-24 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-display text-temple tracking-tight">Z naszego bloga</h2>
          <p className="mt-4 text-lg text-wood-light/80 max-w-2xl mx-auto">
            Przeczytaj najnowsze artykuły o jodze, Bali i podróżach
          </p>
          <div className="mt-6 w-16 h-px bg-temple/20 mx-auto" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {posts.map((post) => (
            <Card
              key={post.slug}
              title={post.title}
              description={post.excerpt}
              link={`/blog/${post.slug}`}
              icon={BookOpen}
              index={post.id}
              imageUrl={post.imageUrl}
            />
          ))}
        </div>
        <div className="mt-12 text-center">
          <Link
            href="/blog"
            className="inline-flex items-center px-6 py-3 bg-rice text-temple text-base font-medium rounded-full shadow-soft border border-temple/10 hover:bg-rice/80 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-temple/20"
            aria-label="Zobacz wszystkie artykuły"
          >
            Zobacz wszystkie artykuły
            <SafeIcon Icon={ArrowRight} className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </section>

      {/* Sekcja Social Media */}
      <section className="py-24 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-display text-temple tracking-tight">Połączmy się</h2>
          <p className="mt-4 text-lg text-wood-light/80 max-w-2xl mx-auto">
            Śledź nas w mediach społecznościowych, aby być na bieżąco
          </p>
          <div className="mt-6 w-16 h-px bg-temple/20 mx-auto" />
        </div>
        <div className="flex flex-wrap justify-center gap-8">
          {socialLinks.map((link) => (
            <a
              key={link.id}
              href={link.href}
              target="_blank"
              rel="noopener noreferrer"
              aria-label={link.aria}
              className="flex flex-col items-center p-6 bg-rice/95 backdrop-blur-sm rounded-xl shadow-soft border border-bamboo/10 hover:shadow-glow hover:-translate-y-0.5 transition-all duration-300"
            >
              <SafeIcon Icon={link.icon} className="w-8 h-8 text-temple mb-3" />
              <span className="text-temple font-medium">{link.label}</span>
            </a>
          ))}
        </div>
      </section>

      {/* Sekcja CTA */}
      <section className="py-24 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto mb-16">
        <div className="bg-gradient-to-r from-temple/10 to-ocean/10 rounded-3xl p-12 text-center">
          <h2 className="text-3xl md:text-4xl font-display text-temple tracking-tight mb-6">
            Gotowa na przygodę życia?
          </h2>
          <p className="text-lg text-wood-light/80 max-w-2xl mx-auto mb-8">
            Dołącz do naszego najbliższego retreatu jogowego na Bali i odkryj harmonię ciała i ducha w otoczeniu tropikalnego raju.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/kontakt"
              className="inline-flex items-center justify-center px-6 py-3 bg-temple text-rice text-base font-medium rounded-full shadow-soft hover:bg-temple/90 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-temple/50"
              aria-label="Skontaktuj się z nami"
            >
              Skontaktuj się z nami
              <SafeIcon Icon={MessageSquare} className="ml-2 h-5 w-5" />
            </Link>
            <Link
              href="/program"
              className="inline-flex items-center justify-center px-6 py-3 bg-rice text-temple text-base font-medium rounded-full shadow-soft border border-temple/10 hover:bg-rice/80 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-temple/20"
              aria-label="Zobacz program"
            >
              Zobacz program
              <SafeIcon Icon={MapPin} className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export { Home };
