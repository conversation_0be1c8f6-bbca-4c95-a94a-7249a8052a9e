import React from 'react';

// Static navigation links to avoid import issues
const staticNavLinks = [
  { href: '/', label: 'Strona główna' },
  { href: '/blog', label: 'Blog' },
  {
    href: '/program',
    label: 'Program',
    submenu: [
      { href: '/program?destination=bali', label: 'Bali - 12 dni' },
      { href: '/program?destination=srilanka', label: 'Sri Lanka - 10 dni' }
    ]
  },
  { href: '/zajecia-online', label: 'Zajęcia Online' },
  { href: '/o-mnie', label: 'O mnie' },
  { href: '/galeria', label: 'Galeria' },
  { href: '/kontakt', label: 'Kontakt' },
];

export default function ServerNavbar() {
  return (
    <>
      <header className="bg-silk/95 backdrop-blur-md border-b border-ink/5 transition-all duration-500 h-25" style={{ height: '100px' }}>
        <div className="max-w-luxury mx-auto px-[16.67%] h-full"> {/* Golden ratio layout */}
          <div className="flex items-center justify-between h-full">
            {/* Logo - BAKASANA ultra minimalistyczne */}
            <a href="/" className="flex items-center group">
              <span className="text-xl font-sans font-extralight text-ink tracking-[0.2em] transition-all duration-500 group-hover:opacity-70">
                BAKASANA
              </span>
            </a>

            {/* Menu ukryte domyślnie, pokazuje się przy hover */}
            <div className="hidden md:block relative group">
              <div className="opacity-0 group-hover:opacity-100 transition-all duration-500 absolute right-0 top-full mt-5">
                <nav className="flex flex-col space-y-4 bg-silk/95 backdrop-blur-md shadow-luxury p-8 min-w-[200px]">
                  {staticNavLinks.filter(link => !link.submenu).map((link) => (
                    <a
                      key={link.href}
                      href={link.href}
                      className="text-sm font-light text-ink/70 hover:opacity-70 transition-all duration-400 tracking-[0.1em] uppercase"
                    >
                      {link.label}
                    </a>
                  ))}
                </nav>
              </div>
            </div>

            {/* BOOK NOW - jedyny złoty element */}
            <a
              href="/kontakt"
              className="bg-temple-gold text-silk px-8 py-3 text-sm font-light tracking-[0.1em] uppercase transition-all duration-400 hover:opacity-70"
            >
              Book Now
            </a>

            {/* Mobile - ultra minimalistyczne */}
            <div className="md:hidden">
              <details className="relative">
                <summary className="text-ink cursor-pointer font-light text-sm uppercase tracking-[0.1em]">
                  Menu
                </summary>
                <nav className="absolute right-0 top-full mt-2 bg-silk/95 backdrop-blur-md shadow-luxury p-6 min-w-[200px]">
                  {staticNavLinks.filter(link => !link.submenu).map((link) => (
                    <a
                      key={link.href}
                      href={link.href}
                      className="block text-sm font-light text-ink/70 hover:opacity-70 transition-all duration-400 py-3 uppercase tracking-[0.1em]"
                    >
                      {link.label}
                    </a>
                  ))}
                  <div className="mt-4 pt-4 border-t border-ink/10">
                    <a
                      href="/kontakt"
                      className="block text-sm font-light text-temple-gold py-2 uppercase tracking-[0.1em]"
                    >
                      Book Now
                    </a>
                  </div>
                </nav>
              </details>
            </div>
          </div>
        </div>
      </header>
    </>
  );
}