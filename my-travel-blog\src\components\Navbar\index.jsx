'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Menu, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { navigationLinks } from '@/data/navigationLinks';

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const pathname = usePathname();
  const clickTimeoutRef = useRef(null);

  // Hydratacja fix
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Zamknij menu po zmianie strony
  useEffect(() => {
    setIsOpen(false);
  }, [pathname]);

  // Cleanup timeout przy unmount
  useEffect(() => {
    return () => {
      if (clickTimeoutRef.current) {
        clearTimeout(clickTimeoutRef.current);
      }
    };
  }, []);

  const navLinks = navigationLinks;

  // Poprawiona funkcja z debouncingiem
  const handleMenuToggle = () => {
    if (!isMounted || clickTimeoutRef.current) return;
    
    clickTimeoutRef.current = setTimeout(() => {
      setIsOpen(prev => !prev);
      clickTimeoutRef.current = null;
    }, 100);
  };

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled ? 'bg-rice/95 backdrop-blur-md shadow-sm' : 'bg-transparent'
      }`}
      suppressHydrationWarning
    >
      <div className="container mx-auto px-4 py-3 md:py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <span className="text-xl font-display font-medium text-temple">
              Fly with bakasana
            </span>
          </Link>

          {/* Nawigacja desktopowa */}
          <nav className="hidden md:flex items-center space-x-8">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="nav-link text-sm font-medium"
                aria-current={pathname === link.href ? 'page' : undefined}
              >
                {link.label}
              </Link>
            ))}
          </nav>

          {/* Przycisk mobilnego menu */}
          <button
            className="md:hidden text-temple"
            onClick={handleMenuToggle}
            aria-label={isOpen ? 'Zamknij menu' : 'Otwórz menu'}
            suppressHydrationWarning
          >
            {isMounted && isOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobilne menu */}
      {isMounted && (
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="md:hidden bg-rice/95 backdrop-blur-md overflow-hidden"
            >
              <nav className="container mx-auto px-4 py-5 flex flex-col space-y-4">
                {navLinks.map((link) => (
                  <Link
                    key={link.href}
                    href={link.href}
                    className="nav-link text-base py-2 block"
                    aria-current={pathname === link.href ? 'page' : undefined}
                    onClick={() => setIsOpen(false)}
                  >
                    {link.label}
                  </Link>
                ))}
              </nav>
            </motion.div>
          )}
        </AnimatePresence>
      )}
    </header>
  );
}