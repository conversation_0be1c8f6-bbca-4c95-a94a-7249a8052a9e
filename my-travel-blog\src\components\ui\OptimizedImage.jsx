'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';

export default function OptimizedImage({
  src,
  alt,
  width,
  height,
  priority = false,
  className = '',
  sizes = '100vw',
  fill = false,
  quality = 80,
  ...props
}) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isIntersecting, setIsIntersecting] = useState(false);
  
  useEffect(() => {
    // Je<PERSON><PERSON> obraz ma priorytet, nie używaj lazy loading
    if (priority) {
      setIsIntersecting(true);
      return;
    }
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsIntersecting(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '200px', // Załaduj obraz, gdy jest 200px od widoku
        threshold: 0.01,
      }
    );
    
    const currentElement = document.getElementById(`image-${src.replace(/\W/g, '')}`);
    if (currentElement) {
      observer.observe(currentElement);
    }
    
    return () => {
      if (currentElement) {
        observer.unobserve(currentElement);
      }
    };
  }, [src, priority]);
  
  return (
    <div 
      id={`image-${src.replace(/\W/g, '')}`}
      className={cn(
        'relative overflow-hidden bg-sand-light/50',
        isLoaded ? 'bg-transparent' : '',
        className
      )}
      style={fill ? { width: '100%', height: '100%', position: 'relative' } : {}}
    >
      {(isIntersecting || priority) && (
        <Image
          src={src}
          alt={alt}
          width={fill ? undefined : width}
          height={fill ? undefined : height}
          quality={quality}
          priority={priority}
          fill={fill}
          sizes={sizes}
          onLoad={() => setIsLoaded(true)}
          className={cn(
            'transition-opacity duration-500',
            isLoaded ? 'opacity-100' : 'opacity-0',
            className
          )}
          {...props}
        />
      )}
    </div>
  );
}