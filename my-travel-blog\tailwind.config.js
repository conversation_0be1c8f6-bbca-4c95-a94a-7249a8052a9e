/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ['class'],
  content: [
    './src/**/*.{js,jsx,ts,tsx}',
    './src/app/**/*.{js,jsx,ts,tsx}',
    './src/components/**/*.{js,jsx,ts,tsx}',
    './src/pages/**/*.{js,jsx,ts,tsx}',
  ],
  corePlugins: {
    preflight: true,
  },
  theme: {
    extend: {
      colors: {
        // ULTRA LUKSUSOWY MINIMALIZM - MONOCHROME + JEDEN AKCENT
        // Podstawa
        'silk': '#FEFDFB',         // Główne tło (zastępuje rice)
        'ink': '#1A1A1A',          // Tekst główny (zastępuje temple)
        'sand': '#F7F5F2',         // Sekcje alternate (zastępuje soft-sage)

        // JEDEN luksusowy akcent
        'temple-gold': '#C9A961',  // TYLKO dla CTA i kluczowych elementów

        // Zachowane dla kompatybilności (będą stopniowo usuwane)
        primary: '#1A1A1A',
        secondary: '#FEFDFB',
        accent: '#C9A961',
      },
      fontFamily: {
        sans: ['Inter', '-apple-system', 'BlinkMacSystemFont', 'sans-serif'],
        serif: ['Inter', '-apple-system', 'BlinkMacSystemFont', 'sans-serif'], // Usuń serif, wszystko sans
        display: ['Inter', '-apple-system', 'BlinkMacSystemFont', 'sans-serif'],
      },
      fontWeight: {
        'extralight': '200',
        'light': '300',
        'normal': '400',
        'medium': '500',
      },
      fontSize: {
        // NOWA HIERARCHIA TYPOGRAFII - ULTRA LUKSUS
        'hero': ['72px', { lineHeight: '1.1', letterSpacing: '-0.03em', fontWeight: '200' }],
        'h2': ['42px', { lineHeight: '1.2', letterSpacing: '-0.02em', fontWeight: '200' }],
        'body': ['17px', { lineHeight: '1.7', fontWeight: '300' }],
        'micro': ['13px', { lineHeight: '1.4', letterSpacing: '0.05em', fontWeight: '400' }],

        // Zachowane dla kompatybilności
        sm: ['13px', { lineHeight: '1.4', letterSpacing: '0.05em', fontWeight: '400' }],
        base: ['17px', { lineHeight: '1.7', fontWeight: '300' }],
        lg: ['20px', { lineHeight: '1.6', fontWeight: '300' }],
        xl: ['24px', { lineHeight: '1.5', letterSpacing: '-0.01em', fontWeight: '300' }],
        '2xl': ['30px', { lineHeight: '1.3', letterSpacing: '-0.01em', fontWeight: '200' }],
        '3xl': ['36px', { lineHeight: '1.2', letterSpacing: '-0.02em', fontWeight: '200' }],
        '4xl': ['48px', { lineHeight: '1.1', letterSpacing: '-0.02em', fontWeight: '200' }],
        '5xl': ['60px', { lineHeight: '1.1', letterSpacing: '-0.03em', fontWeight: '200' }],
        '6xl': ['72px', { lineHeight: '1.1', letterSpacing: '-0.03em', fontWeight: '200' }],
      },
      spacing: {
        xs: '0.5rem',
        sm: '1rem',
        md: '1.5rem',
        lg: '2rem',
        xl: '3rem',
        '2xl': '4rem',
        '3xl': '6rem',    // Dodatkowa przestrzeń dla większej elegancji
        '4xl': '8rem',    // Jeszcze większa przestrzeń
      },
      height: {
        hero: '100vh', // Zawsze 100vh dla ultra luksus
        'hero-sm': '100vh', // Usuń 85vh, zawsze pełny ekran
      },
      maxWidth: {
        content: '80rem',
        '6xl': '72rem',
        'luxury': '42rem', // max-w-2xl dla 40% szerokości
      },
      animation: {
        'fade-in': 'fadeIn 1.2s ease-out forwards',
        'slide-up': 'slideUp 1s ease-out forwards',
        parallax: 'parallax 15s linear infinite',
        'float-gentle': 'floatGentle 6s ease-in-out infinite',
        'scale-in': 'scaleIn 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) forwards',
        'hover-lift': 'hoverLift 0.3s ease-out forwards',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(15px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        parallax: {
          '0%': { backgroundPosition: '0% 0%' },
          '100%': { backgroundPosition: '0% 100%' },
        },
        floatGentle: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-4px)' },
        },
        scaleIn: {
          'from': { opacity: '0', transform: 'scale(0.95)' },
          'to': { opacity: '1', transform: 'scale(1)' },
        },
        hoverLift: {
          'to': { transform: 'translateY(-2px)' },
        },
      },
      transitionTimingFunction: {
        gentle: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        'ease-gentle': 'cubic-bezier(0.23, 1, 0.32, 1)',
        'ease-smooth': 'cubic-bezier(0.4, 0, 0.2, 1)',
        'ease-bounce': 'cubic-bezier(0.34, 1.56, 0.64, 1)',
        'ease-out-smooth': 'cubic-bezier(0.16, 1, 0.3, 1)',
      },
      transitionDuration: {
        gentle: '400ms',
        smooth: '300ms',
        soft: '200ms',
      },
      boxShadow: {
        'luxury': '0 0 40px rgba(0, 0, 0, 0.03)',
        'soft': '0 1px 3px rgba(0, 0, 0, 0.02)',
        'medium': '0 2px 6px rgba(0, 0, 0, 0.03)',
        'subtle': '0 1px 2px rgba(0, 0, 0, 0.01)',
      },
      borderRadius: {
        xl: '0.75rem', 
        lg: '0.5rem',
        md: '0.375rem',
        sm: '0.25rem',
      },
      backgroundImage: {
        'bali-texture': "url('/images/bali-texture.webp')",
        'gradient-hero': 'linear-gradient(135deg, rgba(var(--color-temple), 0.4), rgba(var(--color-ocean), 0.2))',
        'shell-gradient': 'var(--gradient-shell)',
        'warm-gradient': 'var(--gradient-warm)',
        'ocean-gradient': 'var(--gradient-ocean)',
        'sunset-gradient': 'var(--gradient-sunset)',
        'gentle-gradient': 'var(--gradient-gentle)',
      },
      backdropBlur: {
        sm: '4px',
        md: '8px',
        lg: '12px',
        xl: '16px',
      },
    },
  },
  plugins: [],
};